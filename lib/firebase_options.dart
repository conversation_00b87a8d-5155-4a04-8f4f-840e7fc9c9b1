// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCBrin4JHbf5UQQ33vlcqHZCsNd2styN14',
    appId: '1:1087475805155:android:cad717f4aaf879560a4ed6',
    messagingSenderId: '1087475805155',
    projectId: 'mandob-tracker',
    storageBucket: 'mandob-tracker.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA8pw0-NiXpb1shoQ0i1_5hqLLNl44yS7c',
    appId: '1:1087475805155:ios:9e1160a7589721d50a4ed6',
    messagingSenderId: '1087475805155',
    projectId: 'mandob-tracker',
    storageBucket: 'mandob-tracker.firebasestorage.app',
    iosBundleId: 'com.opti4it.mandobTracking',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAUG1owxNxYOapivYcZFm8nQm9Ls1zE6fE',
    appId: '1:1087475805155:web:094e7ed9715688a60a4ed6',
    messagingSenderId: '1087475805155',
    projectId: 'mandob-tracker',
    authDomain: 'mandob-tracker.firebaseapp.com',
    storageBucket: 'mandob-tracker.firebasestorage.app',
    measurementId: 'G-YCP36P88FM',
  );

}