import 'package:flutter/material.dart';

extension StringExtensions on String {
  String get translateErrorMessage {
    //?{"email":"Client Not Found"}

    final errorMessage = this;

    if (!errorMessage.contains(':')) return errorMessage;

    final errorMessageSplitted = errorMessage.split(':');

    final errorKey =
        errorMessageSplitted[0].replaceAll('{', '').replaceAll('"', '');

    final errorValue =
        errorMessageSplitted[1].replaceAll('}', '').replaceAll('"', '');

    return '$errorKey: $errorValue';
  }

  Color get convertHexToColor {
    return isNotEmpty
        ? Color(int.parse('0xFF$this'.replaceAll('#', '')))
        : Colors.transparent;
  }

  String capitalizeFirstLetter() {
    if (this == null || this.isEmpty) return '';
    return "${this[0].toUpperCase()}${this.substring(1).toLowerCase()}";
  }
}
