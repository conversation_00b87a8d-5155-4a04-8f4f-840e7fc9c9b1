import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

extension SVGExtension on String {
  String get svg => 'assets/svg/$this.svg';

  //? on string view svg image
  //? 'logo'.svg.image()
  SvgPicture svgImage({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BoxFit fit = BoxFit.cover,
    AlignmentGeometry alignment = Alignment.center,
    bool matchTextDirection = false,
    String? package,
  }) =>
      SvgPicture.asset(
        this,
        key: key,
        bundle: bundle,
        width: width,
        height: height,
        color: color,
        fit: fit,
        alignment: alignment,
        matchTextDirection: matchTextDirection,
        package: package,
      );
}
