import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/animations/hover_animations.dart';

import '../animations/entrance_fader.dart';
import '../animations/size_transtion.dart';
import '../animations/slide_animation.dart';

extension HoverExtensions on Widget {
  Widget get moveUpOnHover {
    return TranslateOnHover(
      child: this,
    );
  }

  Widget get scaleOnHover {
    return ScaleOnHover(
      child: this,
    );
  }

  Widget sizeTransition({
    Duration duration = const Duration(milliseconds: 100),
    Curve curve = Curves.easeInOut,
    Axis axis = Axis.horizontal,
    double axisAlignment = 0.0,
  }) {
    return SizeTransitionWidget(
      duration: duration,
      curve: curve,
      axis: axis,
      axisAlignment: axisAlignment,
      child: this,
    );
  }

  get horizontalSizeTransition => sizeTransition(axis: Axis.horizontal);

  get verticalSizeTransition => sizeTransition(axis: Axis.vertical);
}

const slideAnimatedDuration = 1700;

extension FadeInExtensions on Widget {
  Widget fadeInFromTop(
      {Duration delay = const Duration(milliseconds: 0),
      Duration duration = const Duration(milliseconds: 400)}) {
    return EntranceFader(
        delay: delay,
        duration: duration,
        direction: FadeDirection.top,
        child: this);
  }

  Widget fadeInFromBottom(
      {Duration delay = const Duration(milliseconds: 0),
      Duration duration = const Duration(milliseconds: 400)}) {
    return EntranceFader(
        delay: delay,
        duration: duration,
        direction: FadeDirection.bottom,
        child: this);
  }

  Widget fadeInFromLeft(
      {Duration delay = const Duration(milliseconds: 0),
      Duration duration = const Duration(milliseconds: 500)}) {
    return EntranceFader(
        delay: delay,
        duration: duration,
        direction: FadeDirection.left,
        child: this);
  }

  Widget fadeInFromRight(
      {Duration delay = const Duration(milliseconds: 0),
      Duration duration = const Duration(milliseconds: 500)}) {
    return EntranceFader(
        delay: delay,
        duration: duration,
        direction: FadeDirection.right,
        child: this);
  }
}

extension SlideAnimationExtensions on Widget {
  Widget rightAnimate({
    Duration duration = const Duration(milliseconds: slideAnimatedDuration),
    SlideAnimationType animationType = SlideAnimationType.right,
    Curve? curve,
  }) {
    return SlideAnimation(
      duration: duration,
      animationType: animationType,
      curve: curve,
      child: this,
    );
  }

  Widget leftAnimate({
    Duration duration = const Duration(milliseconds: slideAnimatedDuration),
    SlideAnimationType animationType = SlideAnimationType.left,
    Curve? curve,
  }) {
    return SlideAnimation(
      duration: duration,
      curve: curve,
      animationType: animationType,
      child: this,
    );
  }

  Widget topAnimate({
    Duration duration = const Duration(milliseconds: slideAnimatedDuration),
    SlideAnimationType animationType = SlideAnimationType.top,
    Curve? curve,
  }) {
    return SlideAnimation(
      duration: duration,
      animationType: animationType,
      curve: curve,
      child: this,
    );
  }

  Widget bottomAnimate({
    Duration duration =
        const Duration(milliseconds: slideAnimatedDuration - 300),
    SlideAnimationType animationType = SlideAnimationType.bottom,
    Curve curve = Curves.easeInOutBack,
  }) {
    return SlideAnimation(
      duration: duration,
      animationType: animationType,
      // curve: curve,
      child: this,
    );
  }

  Widget get leftSlide => leftAnimate();

  Widget get rightSlide => rightAnimate();

  Widget get topSlide => topAnimate();

  Widget get bottomSlide => bottomAnimate();
}
