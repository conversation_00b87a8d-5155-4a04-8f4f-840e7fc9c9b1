import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final settingsRepoProvider = Provider<SettingsLocalRepo>((ref) {
  return SettingsLocalRepo();
});

class SettingsLocalRepo {
  static const String _themeKey = 'theme_mode';

  Future<void> updateLanguage(Locale locale) async {
    GetStorageService.setData(
        key: LocalKeys.language, value: locale.languageCode);
  }

  Future<Locale> locale() async {
    final langCode = await GetStorageService.getData(key: LocalKeys.language);

    if (langCode != null) {
      return Locale(langCode);
    } else {
      return const Locale('en', 'US');
    }
  }

  Future<void> updateThemeMode(ThemeMode themeMode) async {
    GetStorageService.setData(
        key: _themeKey, value: themeMode.name);
  }

  Future<ThemeMode> themeMode() async {
    final themeModeString = await GetStorageService.getData(key: _themeKey);

    if (themeModeString != null) {
      switch (themeModeString) {
        case 'light':
          return ThemeMode.light;
        case 'dark':
          return ThemeMode.dark;
        case 'system':
          return ThemeMode.system;
        default:
          return ThemeMode.system;
      }
    } else {
      return ThemeMode.system;
    }
  }
}
