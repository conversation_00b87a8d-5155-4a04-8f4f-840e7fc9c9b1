import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseTextField extends HookWidget {
  final String name;
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final TextInputType textInputType;
  final Function(String?)? onChanged;
  final TextAlign textAlign;
  final Function()? onTap;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? icon;
  final Widget? suffixIcon;
  final String? label;
  final String? hint;
  final int maxLines;
  final bool isWhiteText;
  final String? ignoringMessage;
  final String? Function(String?)? validator;
  final bool isObscure;
  final bool isRequired;
  final String? initialValue;
  final bool enabled;
  final bool readOnly;
  final bool unFocus;
  final bool withoutEnter;
  final String? title;
  final TextInputAction? textInputAction;
  final Color? fillColor;

  const BaseTextField({
    super.key,
    required this.name,
    this.ignoringMessage,
    this.focusNode,
    this.controller,
    this.isObscure = false,
    this.unFocus = true,
    this.withoutEnter = false,
    this.onTap,
    this.hint,
    this.icon,
    this.fillColor,
    this.suffixIcon,
    this.label,
    this.onChanged,
    this.initialValue,
    this.textAlign = TextAlign.start,
    this.contentPadding = const EdgeInsets.all(AppSpaces.padding12),
    this.textInputType = TextInputType.text,
    this.maxLines = 1,
    this.isWhiteText = false,
    this.isRequired = true,
    this.enabled = true,
    this.readOnly = false,
    this.validator,
    this.textInputAction,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    final isObscureState = useState(isObscure);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: AppTextStyles.labelLarge,
          ),
          AppGaps.gap8,
        ],

        //! Text Field
        _textField(context, isObscureState),
      ],
    );
  }

  Widget _textField(BuildContext context, ValueNotifier<bool> isObscureState) {
    validations() {
      if (textInputType == TextInputType.number) {
        return Validations.numbersOnly;
      } else if (textInputType == TextInputType.emailAddress) {
        return Validations.email;
      } else if (textInputType == TextInputType.phone) {
        return Validations.phoneNumber;
      } else if (textInputType == TextInputType.visiblePassword) {
        return Validations.password;
      }
      return Validations.mustBeNotEmpty;
    }

    return FormBuilderTextField(
      name: name,
      scrollPadding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom + AppSpaces.padding12,
      ),
      style: isWhiteText ? AppTextStyles.whiteLabelLarge : null,
      onTapOutside: (e) => unFocus ? FocusScope.of(context).unfocus() : null,
      focusNode: focusNode,
      obscureText: isObscureState.value,
      textInputAction: textInputAction,
      enabled: enabled,
      readOnly: readOnly,
      controller: controller,
      keyboardType: textInputType,
      inputFormatters: [
        if (textInputType == TextInputType.number)
          FilteringTextInputFormatter.allow(RegExp(r'[0-9.-]'))
      ],
      textAlign: textAlign,
      onTap: onTap,
      onChanged: onChanged,
      initialValue: initialValue,
      maxLines: maxLines,
      validator: isRequired ? (validator ?? validations()) : null,
      decoration: InputDecoration(
        fillColor: fillColor,
        filled: fillColor != null,
        hintText: hint,
        hintStyle: AppTextStyles.whiteLabelMedium.copyWith(
          color: AppColors.grey,
        ),
        labelStyle: isWhiteText
            ? AppTextStyles.whiteLabelLarge
            : AppTextStyles.labelLarge,
        contentPadding: contentPadding,
        labelText: label,
        suffixIcon: textInputType == TextInputType.visiblePassword
            ? InkWell(
                onTap: () {
                  isObscureState.value = !isObscureState.value;
                },
                child: Icon(
                  isObscureState.value
                      ? Icons.visibility_off
                      : Icons.visibility,
                  color: Colors.grey,
                ),
              )
            : Padding(
                padding: const EdgeInsets.all(AppSpaces.padding8),
                child: suffixIcon,
              ),
        prefixIcon: icon != null
            ? Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpaces.padding12,
                  vertical: AppSpaces.padding4,
                ),
                child: icon,
              )
            : null,
      ),
    );
  }
}
