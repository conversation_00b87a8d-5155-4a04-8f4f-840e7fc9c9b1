import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class EmptyDataWidget extends StatelessWidget {
  final String? text;
  const EmptyDataWidget({super.key, this.text});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          const Icon(
            Icons.sentiment_dissatisfied,
            size: 100,
            color: ColorManager.grey,
          ),
          AppGaps.gap12,
          Text(
            text ?? context.tr.noDataFound,
            style: AppTextStyles.title,
          ),
        ],
      ),
    );
  }
}
