import 'package:flutter/material.dart';

import '../../../theme/color_manager.dart';

class LoadingWidget extends StatelessWidget {
  final bool isLinear;
  final Color color;
  final bool isSmall;
  final double? height;
  final double? width;

  const LoadingWidget(
      {super.key,
      this.isLinear = false,
      this.isSmall = false,
      this.height,
      this.width,
      this.color = ColorManager.primaryColor});

  @override
  Widget build(BuildContext context) {
    if (isLinear) {
      return LinearProgressIndicator(
        backgroundColor: ColorManager.primaryColor.withOpacity(0.2),
        valueColor: AlwaysStoppedAnimation<Color>(color),
      );
    }

    return SizedBox(
      height: height,
      width: width,
      child: Image.asset(
        isSmall
            ? 'assets/animated/small_loading.gif'
            : 'assets/animated/loading.gif',
        height: height ?? 100,
        width: width ?? 120,
        fit: BoxFit.cover,
      ),
    );
  }
}
