import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/services/app_settings/controller/settings_controller.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class ChangeLanguageWidget extends HookConsumerWidget {
  const ChangeLanguageWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerProvider);

    final isEng = useState(settingsController.locale.languageCode == 'en');

    final enColor = isEng.value ? ColorManager.primaryColor : Colors.black;

    final arColor = !isEng.value ? ColorManager.primaryColor : Colors.black;

    return AlertDialog(
      surfaceTintColor: Colors.white,
      title: Text(
        context.tr.changeLanguage,
        style: AppTextStyles.title
            .copyWith(fontWeight: FontWeight.bold, fontSize: 22),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            onTap: () {
              isEng.value = !isEng.value;

              settingsController
                  .updateLanguage(Locale(isEng.value ? 'en' : 'ar'));
              navService.back();
            },
            title: Text(context.tr.english,
                style: AppTextStyles.subTitle
                    .copyWith(fontWeight: FontWeight.bold, color: enColor)),
            leading: Icon(
              Icons.language,
              color: enColor,
            ),
          ),
          ListTile(
            onTap: () {
              isEng.value = !isEng.value;

              settingsController
                  .updateLanguage(Locale(isEng.value ? 'en' : 'ar'));
              navService.back();
            },
            title: Text(context.tr.arabic,
                style: AppTextStyles.subTitle
                    .copyWith(fontWeight: FontWeight.bold, color: arColor)),
            leading: Icon(
              Icons.language,
              color: arColor,
            ),
          ),
        ],
      ),
    );
  }
}
