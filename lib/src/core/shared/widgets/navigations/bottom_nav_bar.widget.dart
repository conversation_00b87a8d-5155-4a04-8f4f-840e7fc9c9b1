import 'package:curved_navigation_bar/curved_navigation_bar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../../theme/color_manager.dart';
import 'controller/bottom_nav_bar.controller.dart';

class BottomNavBarWidget extends ConsumerWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(bottomNavigationControllerProvider);

    return CurvedNavigationBar(
      index: selectedIndex,
      backgroundColor: ColorManager.lightGreyBackground,
      buttonBackgroundColor: ColorManager.secondaryColor,
      items: [
        Icon(
          CupertinoIcons.home,
          size: 30.0,
          color: selectedIndex == 0 ? Colors.white : ColorManager.lightGrey,
        ),
        //reports
        Icon(
          FontAwesomeIcons.chartLine,
          size: 30.0,
          color: selectedIndex == 1 ? Colors.white : ColorManager.lightGrey,
        ),
      ],
      onTap: (index) {
        ref
            .read(bottomNavigationControllerProvider.notifier)
            .changeIndex(index);
      },
    );
  }
}
