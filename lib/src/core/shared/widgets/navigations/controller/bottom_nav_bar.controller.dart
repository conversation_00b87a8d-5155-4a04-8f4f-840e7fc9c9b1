import 'package:flutter_riverpod/flutter_riverpod.dart';

// * Bottom Nav Bar Controller ========================================
final bottomNavControllerProvider = Provider<BottomNavigationController>(
  (ref) {
    return BottomNavigationController();
  },
);

// * Bottom Nav Bar State Notifier ========================================
final bottomNavigationControllerProvider =
    StateNotifierProvider<BottomNavigationController, int>(
  (ref) => ref.watch(bottomNavControllerProvider),
);

class BottomNavigationController extends StateNotifier<int> {
  BottomNavigationController() : super(0);

  void changeIndex(int index) {
    state = index;
  }
}
