import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseContainer extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final LinearGradient? gradient;
  final double radius;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final List<BoxShadow>? shadow;
  final Color? borderColor;
  final double borderWidth;
  final Function()? onTap;

  const BaseContainer(
      {super.key,
      required this.child,
      this.width,
      this.color,
      this.borderColor,
      this.margin,
      this.onTap,
      this.gradient,
      this.height,
      this.padding,
      this.borderWidth = 1,
      this.shadow,
      this.radius = AppRadius.radius12});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        margin: margin,
        padding: padding ?? const EdgeInsets.all(AppSpaces.padding12),
        decoration: BoxDecoration(
          gradient: gradient,
          boxShadow: context.isDark ? null : shadow,
          color: color ?? Colors.white,
          borderRadius: BorderRadius.circular(radius),
          border: Border.all(
              color: borderColor ?? const Color(0xFFe8e8e8),
              width: borderWidth),
        ),
        child: child,
      ),
    );
  }
}
