class AcademicStagesModel {
  final int id;
  final String stageName;
  final String image;
  final List<StagesModel> stages;

  AcademicStagesModel({
    required this.id,
    required this.stageName,
    required this.stages,
    required this.image,
  });

  static final academicStages = [
    AcademicStagesModel(
      id: 1,
      stageName: 'Kindergarten',
      image:
          'https://static.vecteezy.com/system/resources/previews/035/593/765/non_2x/ai-generated-happy-boy-cartoon-ai-generative-free-png.png',
      stages: [
        StagesModel(id: 1, stageName: 'KG1'),
        StagesModel(id: 2, stageName: 'KG2'),
        StagesModel(id: 3, stageName: 'KG3'),
      ],
    ),
    AcademicStagesModel(
      id: 2,
      stageName: 'Primary',
      image:
          'https://static.vecteezy.com/system/resources/previews/035/593/765/non_2x/ai-generated-happy-boy-cartoon-ai-generative-free-png.png',
      stages: [
        StagesModel(id: 1, stageName: 'Grade 1'),
        StagesModel(id: 2, stageName: 'Grade 2'),
        StagesModel(id: 3, stageName: 'Grade 3'),
        StagesModel(id: 4, stageName: 'Grade 4'),
        StagesModel(id: 5, stageName: 'Grade 5'),
        StagesModel(id: 6, stageName: 'Grade 6'),
      ],
    ),
    AcademicStagesModel(
      id: 3,
      stageName: 'Middle',
      image:
          'https://static.vecteezy.com/system/resources/previews/035/593/765/non_2x/ai-generated-happy-boy-cartoon-ai-generative-free-png.png',
      stages: [
        StagesModel(id: 1, stageName: 'Grade 7'),
        StagesModel(id: 2, stageName: 'Grade 8'),
        StagesModel(id: 3, stageName: 'Grade 9'),
      ],
    ),
    AcademicStagesModel(
      id: 4,
      stageName: 'High',
      image:
          'https://static.vecteezy.com/system/resources/previews/035/593/765/non_2x/ai-generated-happy-boy-cartoon-ai-generative-free-png.png',
      stages: [
        StagesModel(id: 1, stageName: 'Grade 10'),
        StagesModel(id: 2, stageName: 'Grade 11'),
        StagesModel(id: 3, stageName: 'Grade 12'),
      ],
    ),
  ];
}

class StagesModel {
  final int id;
  final String stageName;

  StagesModel({
    required this.id,
    required this.stageName,
  });
}
