import 'package:flutter/material.dart' show Locale, LocalizationsDelegate;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:opti_tickets/generated/l10n.dart';
import 'package:xr_helper/xr_helper.dart';

class AppConsts {
  static const String appName = 'Opti Tickets';
  static const Locale locale = Locale('en');

  static const List<Locale> supportedLocales = [
    locale,
    Locale('ar'),
  ];

  static bool get isEnglish =>
      GetStorageService.getData(key: LocalKeys.language) == 'en';

  static const List<LocalizationsDelegate> localizationsDelegates = [
    S.delegate,
    FormBuilderLocalizations.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  //? Test Login
  // static const String testEmail = 'HASAB_FSLSC';
  static const String testEmail = 'a.m';
  static const String testPass = '123';
}
