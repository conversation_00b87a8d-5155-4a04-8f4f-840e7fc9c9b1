class ApiEndpoints {
  static const String url = 'http://app.iv4it.com';
  static const String baseUrl = '$url/api/v2.0/emp/portal';

  //? Auth
  static const String login = '$baseUrl/login';

  //? APIs
  static const String home = '$baseUrl/dashboard';
  static const String addReply = '$baseUrl/cl/cp/ticket/add/thread';
  static const String addTicket = '$baseUrl/cl/cp/ticket/cr/act/save';

  static String ticketDetails(int? ticketId) =>
      '$baseUrl/cl/cp/ticket/fetch/details?tid=$ticketId';

  static String ticketReports(String? startDate, String? endDate) =>
      '$baseUrl/cl/cp/tickets/report?from_date=$startDate&to_date=$endDate';
}
