import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti_tickets/src/core/services/file_handler/file_handler.dart';
import 'package:opti_tickets/src/core/services/media/controller/media_controller.dart';
import 'package:opti_tickets/src/core/services/media/ui/pdf_viewer.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class AttachFileIcon extends ConsumerWidget {
  const AttachFileIcon({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaPickerController = ref.watch(mediaPickerControllerProvider);

    return IconButton(
      icon: const Icon(Icons.attach_file),
      onPressed: () async {
        showModalBottomSheet(
            context: context,
            builder: (_) {
              return Padding(
                padding: const EdgeInsets.all(AppSpaces.screenPadding),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      leading: const Icon(Icons.camera, color: Colors.black),
                      title: Text(context.tr.camera),
                      onTap: () async {
                        await mediaPickerController.pickFile(
                          allowMultiple: true,
                          useCamera: true,
                        );
                        navService.back();
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.image, color: Colors.black),
                      title: Text(context.tr.gallery),
                      onTap: () async {
                        await mediaPickerController.pickFile(
                          allowMultiple: true,
                        );
                        navService.back();
                      },
                    ),
                  ],
                ),
              );
            });
      },
    );
  }
}

class ViewLocalSelectedImage extends ConsumerWidget {
  const ViewLocalSelectedImage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaPickerController = ref.watch(mediaPickerControllerProvider);
    final selectedFile = mediaPickerController.filePath;

    final isFile = selectedFile.contains('.pdf') ||
        selectedFile.contains('.doc') ||
        selectedFile.contains('.docx') ||
        selectedFile.contains('.xls') ||
        selectedFile.contains('.xlsx') ||
        selectedFile.contains('.ppt') ||
        selectedFile.contains('.mp4') ||
        selectedFile.contains('.mp3') ||
        selectedFile.contains('.pptx');

    if (selectedFile.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.radius8),
        color: ColorManager.lightGrey.withOpacity(.3),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                if (isFile) {
                  FileHandler.openLocalFile(selectedFile);
                } else {
                  showDialog(
                    context: context,
                    builder: (context) {
                      return Dialog(
                        child: ClipRRect(
                          borderRadius:
                              BorderRadius.circular(AppRadius.radius12),
                          child: Image.file(
                            File(selectedFile),
                            // fit: BoxFit.cover,
                          ),
                        ),
                      );
                    },
                  );
                }
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.attach_file,
                    color: ColorManager.black,
                  ),
                  AppGaps.gap8,
                  Expanded(
                    child: Text(
                      selectedFile.split('/').last,
                      // context.tr.attachment,
                      style: const TextStyle(
                        color: ColorManager.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // delete the selected file
          IconButton(
            icon: const Icon(
              Icons.delete,
              color: ColorManager.errorColor,
            ),
            onPressed: () {
              mediaPickerController.clearFiles();
            },
          ),
        ],
      ),
    );
  }
}

class ViewNetworkFile extends ConsumerWidget {
  final String fileUrl;

  const ViewNetworkFile({
    super.key,
    required this.fileUrl,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isPdf = fileUrl.contains('.pdf');

    if (fileUrl.isEmpty) {
      return const SizedBox.shrink();
    }

    // if (isFile) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.radius8),
        color: ColorManager.lightGrey.withOpacity(.3),
      ),
      child: GestureDetector(
        onTap: () {
          if (isPdf) {
            PdfViewer(url: fileUrl).navigate;
            // FileHandler.openNetworkFile(fileUrl);
          } else {
            showDialog(
              context: context,
              builder: (context) {
                return Dialog(
                  backgroundColor: Colors.transparent,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                    child: Image.network(
                      fileUrl,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return const LoadingWidget();
                      },
                    ),
                  ),
                );
              },
            );
          }
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.attach_file,
              color: ColorManager.black,
            ),
            AppGaps.gap8,
            Text(
              context.tr.attachment,
              style: const TextStyle(
                color: ColorManager.black,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
    // }

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.radius8),
        boxShadow: [
          BoxShadow(
            color: ColorManager.black.withOpacity(.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.radius8),
        child: Stack(
          children: [
            BaseCachedImage(
              fileUrl,
              height: 120,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
            Positioned(
              right: 5,
              bottom: 5,
              child: CircleAvatar(
                backgroundColor: ColorManager.primaryColor,
                child: IconButton(
                  icon: const Icon(
                    Icons.fullscreen,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return Dialog(
                          child: ClipRRect(
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius12),
                            child: BaseCachedImage(
                              fileUrl,
                            ),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Widget buildAddFileContainer(
//   BuildContext context,
//   MediaPickerController mediaPickerController,
// ) {
//   final filesPaths = mediaPickerController.filesPaths;
//   if (filesPaths.isEmpty) {
//     return const SizedBox.shrink();
//   }
//
//   return Row(
//     children: [
//       Card(
//         surfaceTintColor: ColorManager.white,
//         elevation: 2,
//         child: Container(
//           height: 100,
//           width: 100,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(AppRadius.radius12),
//           ),
//           child: IconButton(
//             icon: const Icon(
//               Icons.add,
//               color: ColorManager.black,
//             ),
//             onPressed: () async {
//               // show dialog to ask camera or gallery
//               //await mediaPickerController.pickFile(
//               //                   allowMultiple: true,
//               //                 );
//
//               showModalBottomSheet(
//                   context: context,
//                   builder: (_) {
//                     return Padding(
//                       padding: const EdgeInsets.all(AppSpaces.screenPadding),
//                       child: Column(
//                         mainAxisSize: MainAxisSize.min,
//                         children: [
//                           ListTile(
//                             leading:
//                                 const Icon(Icons.camera, color: Colors.black),
//                             title: Text(context.tr.camera),
//                             onTap: () async {
//                               await mediaPickerController.pickFile(
//                                 allowMultiple: true,
//                                 useCamera: true,
//                               );
//                               Navigator.pop(context);
//                             },
//                           ),
//                           ListTile(
//                             leading:
//                                 const Icon(Icons.image, color: Colors.black),
//                             title: Text(context.tr.gallery),
//                             onTap: () async {
//                               await mediaPickerController.pickFile(
//                                 allowMultiple: true,
//                               );
//                               Navigator.pop(context);
//                             },
//                           ),
//                         ],
//                       ),
//                     );
//                   });
//             },
//           ),
//         ),
//       ),
//     ],
//   );
// }
