import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:opti_tickets/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:xr_helper/xr_helper.dart';

class PdfViewer extends StatelessWidget {
  final String url;

  const PdfViewer({super.key, required this.url});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios_new,
            color: Colors.white,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          url.split('/').last,
          style: AppTextStyles.whiteBody.copyWith(fontSize: 15),
        ),
      ),
      body: const PDF().cachedFromUrl(
        url,
        placeholder: (progress) => const Center(
          child: SizedBox(
            height: 250,
            width: 250,
            child: LoadingWidget(),
          ),
        ),
        errorWidget: (error) => Center(
          child: Text(error.toString()),
        ),
      ),
    );
  }
}

// class PdfPreviewWidget extends StatelessWidget {
//   final String pdfFileUrl;
//
//   const PdfPreviewWidget({super.key, required this.pdfFileUrl});
//
//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       height: 250,
//       child: Stack(
//         children: [
//           PDF(
//             enableSwipe: true,
//             swipeHorizontal: true,
//             autoSpacing: false,
//             pageFling: false,
//             onError: (error) {
//               print(error.toString());
//             },
//             onPageError: (page, error) {
//               print('$page: ${error.toString()}');
//             },
//             onPageChanged: (int? page, int? total) {
//               print('page change: $page/$total');
//             },
//           ).cachedFromUrl(
//             pdfFileUrl,
//             placeholder: (progress) => Center(
//               child: Text('$progress %'),
//             ),
//             errorWidget: (error) => Center(
//               child: Text(error.toString()),
//             ),
//           ),
//           GestureDetector(
//             onTap: () => Navigator.push(
//               context,
//               MaterialPageRoute(
//                 builder: (context) => _PdfViewer(url: pdfFileUrl),
//               ),
//             ),
//             child: Container(
//               height: 250,
//               width: double.infinity,
//               color: Colors.transparent,
//             ),
//           )
//         ],
//       ),
//     );
//   }
// }
