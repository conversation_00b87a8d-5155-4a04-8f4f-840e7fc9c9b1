import 'package:file_picker/file_picker.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xr_helper/xr_helper.dart';

final localMediaRepoProvider = Provider<LocalMediaRepo>((ref) {
  return LocalMediaRepo();
});

class LocalMediaRepo {
  static const int maxFileSize = 10 * 1024 * 1024;

  Future<FilePickerResult?> pickFiles({
    bool imageUpload = true,
    bool uploadMultiple = true,
    bool useCamera = false,
  }) async {
    try {
      await _getPermission();

      if (useCamera) {
        final picker = ImagePicker();
        final pickedFile = await picker.pickImage(source: ImageSource.camera);

        if (pickedFile != null) {
          final fileSize = await pickedFile.length();
          if (fileSize > maxFileSize) {
            Log.e('File size exceeds 10 MB');

            Fluttertoast.showToast(
              msg: 'File size exceeds 10 MB',
              backgroundColor: ColorManager.errorColor,
            );
            return null;
          }

          final file = PlatformFile(
            name: pickedFile.path.split('/').last,
            path: pickedFile.path,
            bytes: await pickedFile.readAsBytes(),
            size: fileSize,
          );

          final result = FilePickerResult([file]);
          return result;
        }
      } else {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: imageUpload ? FileType.image : FileType.any,
          allowMultiple: uploadMultiple,
        );

        if (result != null) {
          for (var file in result.files) {
            if (file.size > maxFileSize) {
              Log.e('File size exceeds 10 MB');
              Fluttertoast.showToast(
                msg: 'File size exceeds 10 MB',
                backgroundColor: ColorManager.errorColor,
              );
              return null;
            }
          }
        }

        return result;
      }
    } catch (e) {
      Log.e('error $e');
      return null;
    }
    return null;
  }

  Future<void> _getPermission() async {
    if (await Permission.storage.isGranted) return;
    try {
      await Permission.storage.request();
    } catch (e) {
      Log.e('error $e');
    }
  }
}
