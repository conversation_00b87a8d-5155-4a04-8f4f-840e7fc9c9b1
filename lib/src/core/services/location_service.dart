import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xr_helper/xr_helper.dart';

class LocationService {
  // * Get current location ================================
  static Future<Position?> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        Log.e('Location services are disabled');
        throw Exception('Location services are disabled. Please enable location services.');
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          Log.e('Location permissions are denied');
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        Log.e('Location permissions are permanently denied');
        throw Exception('Location permissions are permanently denied. Please enable them in settings.');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      Log.i('Location obtained: ${position.latitude}, ${position.longitude}');
      return position;
    } catch (e) {
      Log.e('Get location error: $e');
      rethrow;
    }
  }

  // * Check location permission ================================
  static Future<bool> hasLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      return permission == LocationPermission.always || 
             permission == LocationPermission.whileInUse;
    } catch (e) {
      Log.e('Check location permission error: $e');
      return false;
    }
  }

  // * Request location permission ================================
  static Future<bool> requestLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.requestPermission();
      return permission == LocationPermission.always || 
             permission == LocationPermission.whileInUse;
    } catch (e) {
      Log.e('Request location permission error: $e');
      return false;
    }
  }

  // * Open location settings ================================
  static Future<void> openLocationSettings() async {
    try {
      await Geolocator.openLocationSettings();
    } catch (e) {
      Log.e('Open location settings error: $e');
    }
  }

  // * Calculate distance between two points ================================
  static double calculateDistance({
    required double startLatitude,
    required double startLongitude,
    required double endLatitude,
    required double endLongitude,
  }) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  // * Format coordinates for display ================================
  static String formatCoordinates(double latitude, double longitude) {
    return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
  }

  // * Check if location is within office area (example implementation) ================================
  static bool isWithinOfficeArea({
    required double latitude,
    required double longitude,
    double officeLatitude = 24.7136, // Example: Riyadh coordinates
    double officeLongitude = 46.6753,
    double radiusInMeters = 100, // 100 meters radius
  }) {
    double distance = calculateDistance(
      startLatitude: latitude,
      startLongitude: longitude,
      endLatitude: officeLatitude,
      endLongitude: officeLongitude,
    );
    
    return distance <= radiusInMeters;
  }
}
