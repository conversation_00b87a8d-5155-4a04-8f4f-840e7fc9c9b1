import 'package:open_filex/open_filex.dart';
import 'package:xr_helper/xr_helper.dart';

class FileHandler {
  // static Future<void> openNetworkFile(String url) async {
  //   try {
  //     if (await Permission.storage.request().isGranted) {
  //       final tempDir = await getTemporaryDirectory();
  //       final filePath = '${tempDir.path}/${url.split('/').last}';
  //
  //       final response = await Dio().download(url, filePath,
  //           options: Options(
  //             followRedirects: false,
  //             validateStatus: (status) => status! < 500,
  //           ));
  //
  //       if (response.statusCode == 200) {
  //         OpenResult result = await OpenFilex.open(filePath);
  //
  //         if (result.type == ResultType.noAppToOpen) {
  //           Fluttertoast.showToast(
  //             msg: 'No app to open the file',
  //             backgroundColor: ColorManager.errorColor,
  //           );
  //
  //           await FlutterDownloader.enqueue(
  //             url: url,
  //             savedDir: tempDir.path,
  //             showNotification: true,
  //             saveInPublicStorage: true,
  //             openFileFromNotification: true,
  //           );
  //         }
  //       } else {
  //         Log.e("Error downloading file: ${response.statusCode}");
  //       }
  //     } else {
  //       await Permission.storage.request();
  //       Log.e("Storage permission denied!");
  //     }
  //   } catch (e) {
  //     Log.e("Error opening file: $e");
  //   }
  // }

  /// Opens a file from the device's storage.
  static Future<void> openLocalFile(String filePath) async {
    try {
      OpenFilex.open(filePath);
    } catch (e) {
      Log.e('Error opening file: $e');
    }
  }
}
