import 'package:local_auth/local_auth.dart';
import 'package:xr_helper/xr_helper.dart';

class LocalAuthService {
  static final LocalAuthentication _localAuth = LocalAuthentication();

  // * Check if biometric authentication is available ================================
  static Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      final bool isDeviceSupported = await _localAuth.isDeviceSupported();
      
      Log.i('Biometric available: $isAvailable, Device supported: $isDeviceSupported');
      return isAvailable && isDeviceSupported;
    } catch (e) {
      Log.e('Check biometric availability error: $e');
      return false;
    }
  }

  // * Get available biometric types ================================
  static Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      final List<BiometricType> availableBiometrics = await _localAuth.getAvailableBiometrics();
      Log.i('Available biometrics: $availableBiometrics');
      return availableBiometrics;
    } catch (e) {
      Log.e('Get available biometrics error: $e');
      return [];
    }
  }

  // * Authenticate with biometrics ================================
  static Future<bool> authenticateWithBiometrics({
    String localizedReason = 'Please authenticate to add signature',
    bool biometricOnly = false,
  }) async {
    try {
      final bool isAvailable = await isBiometricAvailable();
      if (!isAvailable) {
        Log.e('Biometric authentication is not available');
        throw Exception('Biometric authentication is not available on this device');
      }

      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: localizedReason,
        options: AuthenticationOptions(
          biometricOnly: biometricOnly,
          stickyAuth: true,
          sensitiveTransaction: true,
        ),
      );

      Log.i('Biometric authentication result: $didAuthenticate');
      return didAuthenticate;
    } catch (e) {
      Log.e('Biometric authentication error: $e');
      rethrow;
    }
  }

  // * Check if fingerprint is available ================================
  static Future<bool> isFingerprintAvailable() async {
    try {
      final List<BiometricType> availableBiometrics = await getAvailableBiometrics();
      return availableBiometrics.contains(BiometricType.fingerprint);
    } catch (e) {
      Log.e('Check fingerprint availability error: $e');
      return false;
    }
  }

  // * Check if face recognition is available ================================
  static Future<bool> isFaceRecognitionAvailable() async {
    try {
      final List<BiometricType> availableBiometrics = await getAvailableBiometrics();
      return availableBiometrics.contains(BiometricType.face);
    } catch (e) {
      Log.e('Check face recognition availability error: $e');
      return false;
    }
  }

  // * Get biometric type string for display ================================
  static String getBiometricTypeString(List<BiometricType> biometrics) {
    if (biometrics.isEmpty) return 'None';
    
    List<String> types = [];
    if (biometrics.contains(BiometricType.fingerprint)) {
      types.add('Fingerprint');
    }
    if (biometrics.contains(BiometricType.face)) {
      types.add('Face');
    }
    if (biometrics.contains(BiometricType.iris)) {
      types.add('Iris');
    }
    if (biometrics.contains(BiometricType.strong)) {
      types.add('Strong');
    }
    if (biometrics.contains(BiometricType.weak)) {
      types.add('Weak');
    }
    
    return types.join(', ');
  }

  // * Stop authentication ================================
  static Future<void> stopAuthentication() async {
    try {
      await _localAuth.stopAuthentication();
      Log.i('Authentication stopped');
    } catch (e) {
      Log.e('Stop authentication error: $e');
    }
  }
}
