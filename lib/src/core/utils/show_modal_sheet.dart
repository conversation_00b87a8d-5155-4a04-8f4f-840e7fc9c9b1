import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

void showModalSheet(
  BuildContext context, {
  required Widget child,
  bool enableDrag = true,
}) {
  final isEng = context.isEnglish;

  showModalBottomSheet(
    enableDrag: enableDrag,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(AppRadius.radius16),
      ),
    ),
    builder: (_) => Platform.isIOS
        ? Padding(
            padding: const EdgeInsets.only(top: AppSpaces.screenPadding),
            child: Stack(
              children: [
                child,
                Positioned(
                  right: isEng ? 10 : null,
                  left: isEng ? null : 10,
                  top: 15,
                  child: IconButton(
                      icon: const Icon(CupertinoIcons.clear),
                      onPressed: () => context.back()),
                )
              ],
            ),
          )
        : child,
    context: context,
  );
}
