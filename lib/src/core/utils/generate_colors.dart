import 'dart:math';

import 'package:flutter/material.dart';

// Color getRandomColor() {
//   final Random random = Random();
//   return Color.fromARGB(
//     255,
//     random.nextInt(256), // Red
//     random.nextInt(256), // Green
//     random.nextInt(256), // Blue
//   );
// }

//getRandomColorByNumber
Color getRandomColorByNumber(int number) {
  final Random random = Random(number);
  return Color.fromARGB(
    255,
    random.nextInt(256), // Red
    random.nextInt(256), // Green
    random.nextInt(256), // Blue
  );
}

Color getTextColor(Color backgroundColor) {
  return backgroundColor.computeLuminance() > 0.5 ? Colors.black : Colors.white;
}
