import 'package:geolocator/geolocator.dart';
import 'package:opti_tickets/src/core/services/firebase_service.dart';
import 'package:opti_tickets/src/core/services/local_auth_service.dart';
import 'package:opti_tickets/src/core/services/location_service.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:xr_helper/xr_helper.dart';

class SignatureRepository with BaseRepository {
  
  // * Add signature ================================
  Future<bool> addSignature({
    required String place,
    bool requireAuth = true,
  }) async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          throw Exception('User not logged in');
        }

        // Authenticate with biometrics if required
        if (requireAuth) {
          final bool authenticated = await LocalAuthService.authenticateWithBiometrics(
            localizedReason: 'Please authenticate to add signature',
          );
          
          if (!authenticated) {
            throw Exception('Authentication failed');
          }
        }

        // Get current location
        final Position? position = await LocationService.getCurrentLocation();
        if (position == null) {
          throw Exception('Unable to get current location');
        }

        final now = DateTime.now();
        final dateString = now.formatDateToString;
        
        // Check if this is the first signature of the day
        final bool hasFirstSignature = await FirebaseService.hasFirstSignatureOfDay(
          uid: currentUser.uid,
          date: dateString,
        );

        // If it's the first signature and time is after 6 AM, set as office
        bool isFirstOfDay = false;
        String finalPlace = place;
        
        if (!hasFirstSignature && now.hour >= 6) {
          isFirstOfDay = true;
          finalPlace = 'المكتب'; // Office in Arabic
        }

        final signature = SignatureModel(
          date: dateString,
          uid: currentUser.uid,
          mandobName: currentUser.name,
          mandobEmail: currentUser.email,
          lat: position.latitude,
          long: position.longitude,
          place: finalPlace,
          timestamp: now,
          isFirstOfDay: isFirstOfDay,
        );

        return await FirebaseService.addSignature(signature);
      },
    );
  }

  // * Get signatures for current user and date ================================
  Future<List<SignatureModel>> getSignaturesByDate({
    required String date,
    String? uid,
  }) async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        final targetUid = uid ?? currentUser.uid;
        
        if (targetUid.isEmpty) {
          throw Exception('User not logged in');
        }

        return await FirebaseService.getSignaturesByUserAndDate(
          uid: targetUid,
          date: date,
        );
      },
    );
  }

  // * Get signatures for date range (admin use) ================================
  Future<List<SignatureModel>> getSignaturesByDateRange({
    required String startDate,
    required String endDate,
    String? uid,
  }) async {
    return baseFunction(
      () async {
        return await FirebaseService.getSignaturesByDateRange(
          startDate: startDate,
          endDate: endDate,
          uid: uid,
        );
      },
    );
  }

  // * Get all users for admin dropdown ================================
  Future<List<UserModel>> getAllUsers() async {
    return baseFunction(
      () async {
        return await FirebaseService.getAllUsers();
      },
    );
  }

  // * Check if user can add first signature ================================
  Future<bool> canAddFirstSignature() async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          return false;
        }

        final now = DateTime.now();
        final dateString = now.formatDateToString;
        
        // Check if already has first signature of the day
        final bool hasFirstSignature = await FirebaseService.hasFirstSignatureOfDay(
          uid: currentUser.uid,
          date: dateString,
        );

        // Can add first signature if it's after 6 AM and no first signature exists
        return !hasFirstSignature && now.hour >= 6;
      },
    );
  }

  // * Get today's signatures for current user ================================
  Future<List<SignatureModel>> getTodaySignatures() async {
    return baseFunction(
      () async {
        final today = DateTime.now().formatDateToString;
        return await getSignaturesByDate(date: today);
      },
    );
  }

  // * Check location permission ================================
  Future<bool> checkLocationPermission() async {
    return baseFunction(
      () async {
        return await LocationService.hasLocationPermission();
      },
    );
  }

  // * Request location permission ================================
  Future<bool> requestLocationPermission() async {
    return baseFunction(
      () async {
        return await LocationService.requestLocationPermission();
      },
    );
  }

  // * Check biometric availability ================================
  Future<bool> checkBiometricAvailability() async {
    return baseFunction(
      () async {
        return await LocalAuthService.isBiometricAvailable();
      },
    );
  }
}
