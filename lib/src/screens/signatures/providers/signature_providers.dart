import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/screens/signatures/controllers/signature_controller.dart';
import 'package:opti_tickets/src/screens/signatures/repositories/signature_repository.dart';

// * Signature Repository Provider ========================================
final signatureRepoProvider = Provider<SignatureRepository>((ref) {
  return SignatureRepository();
});

// * Signature Controller Provider ========================================
final signatureControllerProvider = ChangeNotifierProvider<SignatureController>(
  (ref) {
    final signatureRepo = ref.watch(signatureRepoProvider);

    return SignatureController(
      signatureRepo: signatureRepo,
    );
  },
);

// * Today's Signatures Future Provider ========================================
final todaySignaturesFutureProvider = FutureProvider((ref) async {
  final controller = ref.watch(signatureControllerProvider);
  await controller.getTodaySignatures();
  return controller.signatures;
});

// * Signatures by Date Future Provider ========================================
final signaturesByDateFutureProvider = FutureProvider.family<List<dynamic>, DateTime>((ref, date) async {
  final controller = ref.watch(signatureControllerProvider);
  await controller.getSignaturesByDate(date: date);
  return controller.signatures;
});

// * Users Future Provider (for admin) ========================================
final usersFutureProvider = FutureProvider((ref) async {
  final controller = ref.watch(signatureControllerProvider);
  await controller.getAllUsers();
  return controller.users;
});

// * Can Add First Signature Provider ========================================
final canAddFirstSignatureProvider = FutureProvider<bool>((ref) async {
  final controller = ref.watch(signatureControllerProvider);
  await controller.checkCanAddFirstSignature();
  return controller.canAddFirstSignature;
});

// * Location Permission Provider ========================================
final locationPermissionProvider = FutureProvider<bool>((ref) async {
  final controller = ref.watch(signatureControllerProvider);
  return await controller.checkLocationPermission();
});

// * Biometric Availability Provider ========================================
final biometricAvailabilityProvider = FutureProvider<bool>((ref) async {
  final controller = ref.watch(signatureControllerProvider);
  return await controller.checkBiometricAvailability();
});
