import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

class SignatureModel extends Equatable {
  final String id;
  final String date;
  final String uid;
  final String mandobName;
  final String mandobEmail;
  final double lat;
  final double long;
  final String place;
  final DateTime? timestamp;
  final bool isFirstOfDay;

  const SignatureModel({
    this.id = '',
    this.date = '',
    this.uid = '',
    this.mandobName = '',
    this.mandobEmail = '',
    this.lat = 0.0,
    this.long = 0.0,
    this.place = '',
    this.timestamp,
    this.isFirstOfDay = false,
  });

  // * From Firestore Json ================================
  factory SignatureModel.fromJson(Map<String, dynamic> json) {
    return SignatureModel(
      id: json['id'] ?? '',
      date: json['date'] ?? '',
      uid: json['uid'] ?? '',
      mandobName: json['mandob_name'] ?? '',
      mandobEmail: json['mandob_email'] ?? '',
      lat: (json['lat'] ?? 0.0).toDouble(),
      long: (json['long'] ?? 0.0).toDouble(),
      place: json['place'] ?? '',
      timestamp: json['timestamp'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['timestamp'])
          : DateTime.now(),
      isFirstOfDay: json['isFirstOfDay'] ?? false,
    );
  }

  // * To Firestore Json ================================
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date,
      'uid': uid,
      'mandob_name': mandobName,
      'mandob_email': mandobEmail,
      'lat': lat,
      'long': long,
      'place': place,
      'timestamp': timestamp?.millisecondsSinceEpoch ??
          DateTime.now().millisecondsSinceEpoch,
      'isFirstOfDay': isFirstOfDay,
      'createdAt': DateTime.now().formatDateToString,
    };
  }

  // * Copy With ================================
  SignatureModel copyWith({
    String? id,
    String? date,
    String? uid,
    String? mandobName,
    String? mandobEmail,
    double? lat,
    double? long,
    String? place,
    DateTime? timestamp,
    bool? isFirstOfDay,
  }) {
    return SignatureModel(
      id: id ?? this.id,
      date: date ?? this.date,
      uid: uid ?? this.uid,
      mandobName: mandobName ?? this.mandobName,
      mandobEmail: mandobEmail ?? this.mandobEmail,
      lat: lat ?? this.lat,
      long: long ?? this.long,
      place: place ?? this.place,
      timestamp: timestamp ?? this.timestamp,
      isFirstOfDay: isFirstOfDay ?? this.isFirstOfDay,
    );
  }

  @override
  List<Object?> get props => [
        id,
        date,
        uid,
        mandobName,
        mandobEmail,
        lat,
        long,
        place,
        timestamp,
        isFirstOfDay,
      ];

  // * Helper methods ================================
  bool get isOfficeSignature => place == 'المكتب' || place == 'Office';

  String get formattedTime =>
      '${timestamp?.hour.toString().padLeft(2, '0')}:${timestamp?.minute.toString().padLeft(2, '0')}';

  String get formattedDate => timestamp.formatDateToString;
}
