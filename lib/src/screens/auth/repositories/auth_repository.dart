import 'package:xr_helper/xr_helper.dart';

class AuthRepository with BaseRepository {
  final BaseApiServices networkApiService;

  AuthRepository({
    required this.networkApiService,
  });

  // * Login
  // Future<bool> login({
  //   required UserModel user,
  // }) async {
  //   return baseFunction(
  //     () async {
  //       final fcmToken = '123124'; //TODO-fix
  //       // kIsWeb ? '' : await NotificationService.getToken();
  //       final deviceType = 'ios'; //TODO-fix
  //       // kIsWeb || Platform.isAndroid ? 'Android' : 'iOS';
  //
  //       final params =
  //           '?username=${user.username}&password=${user.password}&type=mob&device_type=$deviceType&token=$fcmToken';
  //
  //       final url = ApiEndpoints.login + params;
  //
  //       final response = await networkApiService.postResponse(
  //         url,
  //         body: {},
  //       );
  //
  //       saveUserData(response);
  //
  //       return true;
  //     },
  //   );
  // }
  //
  // // * Save to local
  // void saveUserData(Map<String, dynamic> data) {
  //   // final userData = UserModel.fromJson(data['data']['user']);
  //   //
  //   // GetStorageService.setData(
  //   //   key: LocalKeys.user,
  //   //   value: userData.toJson(),
  //   // );
  //
  //   GetStorageService.setData(
  //     key: LocalKeys.token,
  //     value: data['token'],
  //   );
  // }
  //
  Future<void> logout() async {
    await baseFunction(
      () async {
        final lang = GetStorageService.getData(key: LocalKeys.language);
        //! Clear Local Data
        GetStorageService.clearLocalData();
        GetStorageService.setData(key: LocalKeys.language, value: lang);
      },
    );
  }
}
