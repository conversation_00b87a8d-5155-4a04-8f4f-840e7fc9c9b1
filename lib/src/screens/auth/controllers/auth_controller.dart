import 'package:opti_tickets/src/core/consts/network/api_strings.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/auth/repositories/auth_repository.dart';
import 'package:opti_tickets/src/screens/auth/view/login/login.screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../splash/view/selected_screen.dart';

class AuthController extends BaseVM {
  final AuthRepository authRepo;

  AuthController({
    required this.authRepo,
  });

  // * Login
  Future<bool> login({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        final user = await _setUser(data);

        final userData = await authRepo.login(user: user);

        return userData;
      },
      additionalFunction: () {
        const SelectedScreen().navigateReplacement;
      },
    );
  }

  // * Set User
  Future<UserModel> _setUser(
    Map<String, dynamic> data,
  ) async {
    final user = UserModel(
      username: data[FieldsConsts.username],
      password: data[FieldsConsts.password],
    );

    return user;
  }

  // * Logout
  Future<void> logout() async {
    return await baseFunction(
      () async {
        await authRepo.logout();

        navService.back();

        const LoginScreen().navigateReplacement;
      },
    );
  }
}
