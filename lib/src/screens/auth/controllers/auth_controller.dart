import 'package:opti_tickets/src/core/consts/network/api_strings.dart';
import 'package:opti_tickets/src/core/services/firebase_service.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/auth/repositories/auth_repository.dart';
import 'package:opti_tickets/src/screens/auth/view/login/login.screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../splash/view/selected_screen.dart';

class AuthController extends BaseVM {
  final AuthRepository authRepo;

  AuthController({
    required this.authRepo,
  });

  // * Login with Firebase
  Future<bool> login({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        final email = data[FieldsConsts.username] ?? '';
        final password = data[FieldsConsts.password] ?? '';

        final user = await FirebaseService.loginUser(
          email: email,
          password: password,
        );

        if (user != null) {
          return true;
        } else {
          throw Exception('Login failed');
        }
      },
      additionalFunction: () {
        const SelectedScreen().navigateReplacement;
      },
    );
  }

  // * Register with Firebase
  Future<bool> register({
    required String email,
    required String password,
    required String name,
    bool isAdmin = false,
  }) async {
    return await baseFunction(
      () async {
        final user = await FirebaseService.registerUser(
          email: email,
          password: password,
          name: name,
          isAdmin: isAdmin,
        );

        if (user != null) {
          return true;
        } else {
          throw Exception('Registration failed');
        }
      },
      additionalFunction: () {
        const SelectedScreen().navigateReplacement;
      },
    );
  }

  // * Set User
  Future<UserModel> _setUser(
    Map<String, dynamic> data,
  ) async {
    final user = UserModel(
      username: data[FieldsConsts.username],
      password: data[FieldsConsts.password],
    );

    return user;
  }

  // * Logout with Firebase
  Future<void> logout() async {
    return await baseFunction(
      () async {
        await FirebaseService.logoutUser();

        navService.back();

        const LoginScreen().navigateReplacement;
      },
    );
  }
}
