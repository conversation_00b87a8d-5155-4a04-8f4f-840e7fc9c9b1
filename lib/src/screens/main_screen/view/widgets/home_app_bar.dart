import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/change_language/change_language.widget.dart';
import 'package:opti_tickets/src/screens/auth/providers/auth_providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/theme/color_manager.dart';

class HomeAppBar extends ConsumerWidget {
  final String name;
  final bool fromHome;

  const HomeAppBar({
    super.key,
    required this.name,
    this.fromHome = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: 130.h,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpaces.screenPadding,
      ),
      decoration: const BoxDecoration(
        color: ColorManager.primaryColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppRadius.radius24),
          bottomRight: Radius.circular(AppRadius.radius24),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (fromHome)
            Expanded(
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: "${context.tr.welcomeBack},\n",
                      style: AppTextStyles.whiteSubHeadLine.copyWith(
                          fontFamily: GoogleFonts.alexandria().fontFamily),
                    ),
                    TextSpan(
                      text: name,
                      style: AppTextStyles.whiteSubTitle.copyWith(
                          height: 1.5,
                          fontFamily: GoogleFonts.alexandria().fontFamily),
                    ),
                  ],
                ),
              ),
            )
          else
            Text(
              name,
              style: AppTextStyles.whiteTitle.copyWith(
                height: 1.5,
              ),
            ),

          AppGaps.gap8,

          Row(
            children: [
              //! Change Language
              IconButton(
                  onPressed: () => showDialog(
                      context: context,
                      builder: (_) => const ChangeLanguageWidget()),
                  icon: const Icon(
                    Icons.language,
                    color: ColorManager.secondaryColor,
                  )).decorated(
                width: 45,
                color: ColorManager.white,
                radius: BorderRadius.circular(AppRadius.radius8),
                margin: const EdgeInsets.only(
                  top: AppSpaces.screenPadding,
                ),
              ),

              IconButton(
                onPressed: () => showDialog(
                    context: context, builder: (_) => const LogoutDialog()),
                icon: const Icon(
                  Icons.logout,
                  color: ColorManager.errorColor,
                ),
              ).decorated(
                width: 45,
                color: ColorManager.white,
                radius: BorderRadius.circular(AppRadius.radius8),
                margin: const EdgeInsets.only(
                  top: AppSpaces.screenPadding,
                  right: AppSpaces.screenPadding,
                  left: AppSpaces.screenPadding,
                ),
              ),
            ],
          )
          // Assets.images.logo.svgImage(),
        ],
      ),
    );
  }
}

class LogoutDialog extends HookConsumerWidget {
  const LogoutDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerProvider);

    return AlertDialog(
      surfaceTintColor: Colors.white,
      title: Text(
        context.tr.logout,
        style: AppTextStyles.title
            .copyWith(fontWeight: FontWeight.bold, fontSize: 22),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            context.tr.areYouSureYouWantToLogout,
            style: AppTextStyles.subTitle.copyWith(fontWeight: FontWeight.bold),
          ),
          AppGaps.gap24,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: () {
                  navService.back();
                },
                child: Text(
                  context.tr.cancel,
                  style: AppTextStyles.labelLarge,
                ),
              ),
              TextButton(
                onPressed: () {
                  authController.logout();
                },
                child: Text(context.tr.confirm,
                    style: AppTextStyles.labelLarge.copyWith(
                      color: ColorManager.errorColor,
                    )),
              ),
            ],
          )
        ],
      ),
    );
  }
}
