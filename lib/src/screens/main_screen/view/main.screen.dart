// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
// import 'package:opti_tickets/src/core/theme/color_manager.dart';
// import 'package:opti_tickets/src/core/utils/show_modal_sheet.dart';
// import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
// import 'package:opti_tickets/src/screens/home/<USER>/home_providers.dart';
// import 'package:opti_tickets/src/screens/main_screen/view/widgets/home_app_bar.dart';
// import 'package:opti_tickets/src/screens/main_screen/view/widgets/programs_slider.widget.dart';
// import 'package:opti_tickets/src/screens/tickets/view/add_ticket_sheet/add_ticket_sheet.widget.dart';
// import 'package:opti_tickets/src/screens/tickets/view/widgets/ticket_card.dart';
// import 'package:skeletonizer/skeletonizer.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class MainScreen extends ConsumerWidget {
//   const MainScreen({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final homeFuture = ref.watch(getHomeFutureProvider);
//
//     final homeModel = homeFuture.when(
//       data: (homeData) => homeData,
//       loading: () => const HomeModel(),
//       error: (error, stackTrace) => const HomeModel(),
//     );
//
//     final isLoading = homeFuture.isLoading;
//
//     final homeData = isLoading ? demoHomeModel : homeModel;
//
//     return Scaffold(
//       backgroundColor: ColorManager.lightGreyBackground,
//       floatingActionButton: FloatingActionButton(
//         backgroundColor: ColorManager.secondaryColor,
//         onPressed: () {
//           showModalSheet(
//             context,
//             child: const AddTicketSheetWidget(),
//           );
//         },
//         child: const Icon(Icons.add),
//       ),
//       body: CustomScrollView(
//         slivers: [
//           SliverAppBar(
//             automaticallyImplyLeading: false,
//             pinned: true,
//             expandedHeight: 110.h,
//             shape: const RoundedRectangleBorder(
//               borderRadius: BorderRadius.only(
//                 bottomLeft: Radius.circular(AppRadius.radius24),
//                 bottomRight: Radius.circular(AppRadius.radius24),
//               ),
//             ),
//             flexibleSpace: FlexibleSpaceBar(
//               background: Skeletonizer(
//                 enabled: isLoading,
//                 child: HomeAppBar(
//                   name: context.isEnglish
//                       ? homeData.clientNameEn
//                       : homeData.clientNameAr,
//                 ),
//               ),
//             ),
//           ),
//           const SliverToBoxAdapter(
//             child: AppGaps.gap16,
//           ),
//           SliverToBoxAdapter(
//             child: Skeletonizer(
//               enabled: isLoading,
//               child: ProgramsSliderWidget(
//                 programs: homeData.programs,
//               ),
//             ),
//           ),
//           const SliverToBoxAdapter(
//             child: AppGaps.gap16,
//           ),
//           SliverToBoxAdapter(
//             child: Skeletonizer(
//               enabled: isLoading,
//               child: Padding(
//                 padding:
//                     const EdgeInsets.symmetric(horizontal: AppSpaces.padding16),
//                 child: Text(
//                   context.tr.recentActiveTickets,
//                   style: AppTextStyles.title,
//                 ),
//               ),
//             ),
//           ),
//           const SliverToBoxAdapter(
//             child: AppGaps.gap16,
//           ),
//           SliverPadding(
//             padding: const EdgeInsets.symmetric(
//               horizontal: AppSpaces.padding16,
//             ),
//             sliver: SliverList(
//               delegate: SliverChildBuilderDelegate(
//                 (context, index) {
//                   final ticket = homeData.tickets[index];
//                   return Skeletonizer(
//                     enabled: isLoading,
//                     child: Padding(
//                       padding: const EdgeInsets.only(
//                         bottom: AppSpaces.padding16,
//                       ),
//                       child: TicketCard(
//                         ticket: ticket,
//                       ),
//                     ),
//                   );
//                 },
//                 childCount: homeData.tickets.length,
//               ),
//             ),
//           ),
//           const SliverToBoxAdapter(
//             child: AppGaps.gap16,
//           ),
//         ],
//       ),
//     );
//   }
// }
