import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeController extends BaseVM {
  final HomeRepository homeRepo;

  HomeController({
    required this.homeRepo,
  });

  // * Get Home
  Future<HomeModel> getHome() async {
    return await baseFunction(
      () async {
        return await homeRepo.getHome();
      },
    );
  }
}
