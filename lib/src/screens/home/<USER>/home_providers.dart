import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/home_controller.dart';
import '../repositories/home_repository.dart';

// * Home Repo Provider ========================================
final homeRepoProvider = Provider<HomeRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return HomeRepository(networkApiService: networkApiService);
});

// * Home Change Notifier Provider ========================================
final homeControllerNotifierProvider = ChangeNotifierProvider<HomeController>(
  (ref) {
    final homeRepo = ref.watch(homeRepoProvider);

    return HomeController(
      homeRepo: homeRepo,
    );
  },
);

// * Home Provider ========================================
final homeControllerProvider = Provider<HomeController>(
  (ref) {
    final homeRepo = ref.watch(homeRepoProvider);

    return HomeController(
      homeRepo: homeRepo,
    );
  },
);

// * Get Home Future Provider ========================================
final getHomeFutureProvider = FutureProvider(
  (
    ref,
  ) {
    final homeController = ref.watch(homeControllerProvider);

    return homeController.getHome();
  },
);
