import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/change_language/change_language.widget.dart';
import 'package:opti_tickets/src/screens/auth/providers/auth_providers.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/theme/color_manager.dart';
import '../../../settings/settings_screen.dart';

class HomeAppBarWidget extends ConsumerWidget {
  final Employee employee;

  const HomeAppBarWidget({
    super.key,
    required this.employee,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: 130.h,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpaces.screenPadding,
      ),
      decoration: const BoxDecoration(
        color: ColorManager.primaryColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppRadius.radius24),
          bottomRight: Radius.circular(AppRadius.radius24),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // User info section
          Expanded(
            child: Row(
              children: [
                // User avatar
                GestureDetector(
                  onTap: () {
                    SettingsScreen(
                      employee: employee,
                    ).navigate;
                  },
                  child: CircleAvatar(
                    radius: 25.r,
                    backgroundColor: ColorManager.white,
                    child: employee.empPIC.isNotEmpty
                        ? ClipOval(
                            child: Image.network(
                              employee.empPIC,
                              width: 50.r,
                              height: 50.r,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.person,
                                  size: 30.r,
                                  color: ColorManager.primaryColor,
                                );
                              },
                            ),
                          )
                        : Icon(
                            Icons.person,
                            size: 30.r,
                            color: ColorManager.primaryColor,
                          ),
                  ),
                ),
                AppGaps.gap12,
                // User name and job
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        employee.empName.isNotEmpty
                            ? employee.empName
                            : context.tr.welcomeBack,
                        style: AppTextStyles.whiteTitle.copyWith(
                          fontFamily: GoogleFonts.alexandria().fontFamily,
                          fontSize: 18.sp,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (employee.empJob.isNotEmpty) ...[
                        AppGaps.gap4,
                        Text(
                          employee.empJob,
                          style: AppTextStyles.whiteSubTitle.copyWith(
                            fontFamily: GoogleFonts.alexandria().fontFamily,
                            fontSize: 14.sp,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),

          AppGaps.gap8,

          // Action buttons
          Row(
            children: [
              //! Change Language
              // IconButton(
              //   onPressed: () => showDialog(
              //     context: context,
              //     builder: (_) => const ChangeLanguageWidget(),
              //   ),
              //   icon: const Icon(
              //     Icons.language,
              //     color: ColorManager.secondaryColor,
              //   ),
              // ).decorated(
              //   width: 45,
              //   color: ColorManager.white,
              //   radius: BorderRadius.circular(AppRadius.radius8),
              //   margin: const EdgeInsets.only(
              //     top: AppSpaces.screenPadding,
              //   ),
              // ),

              IconButton(
                onPressed: () => showDialog(
                  context: context,
                  builder: (_) => const LogoutDialog(),
                ),
                icon: const Icon(
                  Icons.logout,
                  color: ColorManager.errorColor,
                ),
              ).decorated(
                width: 45,
                color: ColorManager.white,
                radius: BorderRadius.circular(AppRadius.radius8),
                margin: const EdgeInsets.only(
                  top: AppSpaces.screenPadding,
                  right: AppSpaces.screenPadding,
                  left: AppSpaces.screenPadding,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}

class LogoutDialog extends HookConsumerWidget {
  const LogoutDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerProvider);

    return AlertDialog(
      surfaceTintColor: Colors.white,
      title: Text(
        context.tr.logout,
        style: AppTextStyles.title
            .copyWith(fontWeight: FontWeight.bold, fontSize: 22),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            context.tr.areYouSureYouWantToLogout,
            style: AppTextStyles.subTitle.copyWith(fontWeight: FontWeight.bold),
          ),
          AppGaps.gap24,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: () {
                  navService.back();
                },
                child: Text(
                  context.tr.cancel,
                  style: AppTextStyles.labelLarge,
                ),
              ),
              TextButton(
                onPressed: () {
                  authController.logout();
                },
                child: Text(
                  context.tr.confirm,
                  style: AppTextStyles.labelLarge.copyWith(
                    color: ColorManager.errorColor,
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
