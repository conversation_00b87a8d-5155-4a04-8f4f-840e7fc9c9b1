import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
import 'package:xr_helper/xr_helper.dart';

class StatsCardWidget extends StatelessWidget {
  final CurrentMonthStats stats;

  const StatsCardWidget({
    super.key,
    required this.stats,
  });

  @override
  Widget build(BuildContext context) {
    final total = stats.total;

    // Define colors for each stat type
    final List<StatsItem> statsItems = [
      StatsItem(
        name: context.tr.completeAttends,
        value: stats.totalCompleteAttends,
        color: Colors.green,
      ),
      StatsItem(
        name: context.tr.incompleteAttends,
        value: stats.totalIncompleteAttends,
        color: Colors.orange,
      ),
      StatsItem(
        name: context.tr.absents,
        value: stats.totalAbsents,
        color: Colors.red,
      ),
      StatsItem(
        name: context.tr.officialHolidays,
        value: stats.totalOfficialHolidays,
        color: Colors.blue,
      ),
      StatsItem(
        name: context.tr.requestLeaves,
        value: stats.totalRequestLeaves,
        color: Colors.purple,
      ),
      StatsItem(
        name: context.tr.sickLeaves,
        value: stats.totalSickLeaves,
        color: Colors.teal,
      ),
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),
      padding: const EdgeInsets.all(AppSpaces.padding20),
      decoration: BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.circular(AppRadius.radius16),
        boxShadow: [
          BoxShadow(
            color: ColorManager.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                context.tr.summary,
                style: AppTextStyles.title.copyWith(
                  fontFamily: GoogleFonts.alexandria().fontFamily,
                  fontWeight: FontWeight.bold,
                  fontSize: 18.sp,
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: 8.w),
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: ColorManager.lightGreyBackground,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  DateFormat('MMMM').format(DateTime.now()),
                  style: AppTextStyles.subTitle.copyWith(
                    fontFamily: GoogleFonts.alexandria().fontFamily,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          AppGaps.gap16,

          // Progress bar showing proportions
          if (total > 0) ...[
            Container(
              height: 8.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.r),
                color: ColorManager.lightGrey,
              ),
              child: Row(
                children: statsItems.map((item) {
                  final percentage = item.value / total;
                  return Expanded(
                    flex: (percentage * 100).round(),
                    child: Container(
                      decoration: BoxDecoration(
                        color: item.color,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
            AppGaps.gap24,
          ],

          // Stats list
          Column(
            children: statsItems.map((item) {
              return Padding(
                padding: EdgeInsets.only(bottom: 12.h),
                child: Row(
                  children: [
                    // Color indicator
                    Container(
                      width: 12.r,
                      height: 12.r,
                      decoration: BoxDecoration(
                        color: item.color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    AppGaps.gap12,
                    // Stat name
                    Expanded(
                      child: Text(
                        item.name,
                        style: AppTextStyles.subTitle.copyWith(
                          fontFamily: GoogleFonts.alexandria().fontFamily,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    // Stat value
                    Text(
                      item.value.toString(),
                      style: AppTextStyles.title.copyWith(
                        fontFamily: GoogleFonts.alexandria().fontFamily,
                        fontWeight: FontWeight.bold,
                        fontSize: 16.sp,
                        color: item.color,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}

class StatsItem {
  final String name;
  final int value;
  final Color color;

  StatsItem({
    required this.name,
    required this.value,
    required this.color,
  });
}
