import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class AttendanceCardWidget extends StatelessWidget {
  final String attendance;
  final String workTime;

  const AttendanceCardWidget({
    super.key,
    required this.attendance,
    required this.workTime,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),
      padding: const EdgeInsets.all(AppSpaces.padding20),
      decoration: BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.circular(AppRadius.radius16),
        boxShadow: [
          BoxShadow(
            color: ColorManager.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            attendance.isEmpty ? '--:--' : attendance,
            style: AppTextStyles.title.copyWith(
              fontSize: 28.sp,
              fontWeight: FontWeight.bold,
              color: ColorManager.primaryColor,
            ),
          ),

          AppGaps.gap16,

          Text(
            workTime,
            style: AppTextStyles.title.copyWith(
              fontSize: 14.sp,
              color: ColorManager.secondaryColor,
            ),
          ),

          AppGaps.gap16,

          // Divider
          Container(
            height: 1,
            color: ColorManager.lightGrey,
          ),

          AppGaps.gap16,

          // button for check in with fingerprint icon
          Button(
            onPressed: () {},
            label: context.tr.checkIn,
            isPrefixIcon: true,
            icon: const Icon(
              Icons.fingerprint,
              color: ColorManager.white,
              size: 26,
            ),
          )
        ],
      ),
    );
  }
}
