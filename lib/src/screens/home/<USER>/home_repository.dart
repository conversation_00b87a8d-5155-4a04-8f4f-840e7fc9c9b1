import 'package:opti_tickets/src/core/consts/network/api_endpoints.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeRepository with BaseRepository {
  final BaseApiServices networkApiService;

  HomeRepository({
    required this.networkApiService,
  });

  // * Get Home
  Future<HomeModel> getHome() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.home;

        final response = await networkApiService.getResponse(
          url,
        );

        if (response == null ||
            response['dt'] == null ||
            response['dt'].isEmpty) {
          return HomeModel.empty();
        }

        final homeModel = HomeModel.fromJson(response);

        return homeModel;
      },
    );
  }
}
