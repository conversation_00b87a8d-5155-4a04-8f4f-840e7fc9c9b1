import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_providers.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/attendance_card_widget.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/home_app_bar_widget.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/stats_card_widget.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeFuture = ref.watch(getHomeFutureProvider);

    final homeModel = homeFuture.when(
      data: (homeData) => homeData,
      loading: () => const HomeModel(),
      error: (error, stackTrace) => const HomeModel(),
    );

    final isLoading = homeFuture.isLoading;
    final homeData = isLoading ? demoHomeModel : homeModel;

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            automaticallyImplyLeading: false,
            pinned: true,
            expandedHeight: 110.h,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(AppRadius.radius24),
                bottomRight: Radius.circular(AppRadius.radius24),
              ),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Skeletonizer(
                enabled: isLoading,
                child: HomeAppBarWidget(
                  employee: homeData.employee,
                ),
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
          SliverToBoxAdapter(
            child: Skeletonizer(
              enabled: isLoading,
              child: AttendanceCardWidget(
                attendance: homeData.attendance,
                workTime: 'Work Time: 9:00 AM - 5:00 PM',
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
          SliverToBoxAdapter(
            child: Skeletonizer(
              enabled: isLoading,
              child: StatsCardWidget(
                stats: homeData.currentMonthStats,
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
        ],
      ),
    );
  }
}
