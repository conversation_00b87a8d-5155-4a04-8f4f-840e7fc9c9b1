import 'package:equatable/equatable.dart';

class HomeModel extends Equatable {
  final Employee employee;
  final String attendance;
  final CurrentMonthStats currentMonthStats;
  final Tasks tasks;
  final List<Contract> contractsAboutExpire;

  const HomeModel({
    this.employee = const Employee(),
    this.attendance = '',
    this.currentMonthStats = const CurrentMonthStats(),
    this.tasks = const Tasks(),
    this.contractsAboutExpire = const [],
  });

  // * From Json
  factory HomeModel.fromJson(Map<String, dynamic> json) {
    final dt = json['dt'] ?? {};
    return HomeModel(
      employee: Employee.from<PERSON>son(dt['employee'] ?? {}),
      attendance: dt['attendance'] ?? '',
      currentMonthStats:
          CurrentMonthStats.fromJson(dt['current_month_stats'] ?? {}),
      tasks: Tasks.fromJson(dt['tasks'] ?? {}),
      contractsAboutExpire: (dt['contracts_about_expire'] as List<dynamic>?)
              ?.map((e) => Contract.fromJson(e))
              .toList() ??
          [],
    );
  }

  factory HomeModel.empty() => const HomeModel();

  @override
  List<Object?> get props =>
      [employee, attendance, currentMonthStats, tasks, contractsAboutExpire];
}

class Employee extends Equatable {
  final String empPIC;
  final String empName;
  final String empJob;

  const Employee({
    this.empPIC = '',
    this.empName = '',
    this.empJob = '',
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      empPIC: json['empPIC'] ?? '',
      empName: json['empName'] ?? '',
      empJob: json['empJob'] ?? '',
    );
  }

  @override
  List<Object?> get props => [empPIC, empName, empJob];
}

class CurrentMonthStats extends Equatable {
  final int totalCompleteAttends;
  final int totalIncompleteAttends;
  final int totalAbsents;
  final int totalOfficialHolidays;
  final int totalRequestLeaves;
  final int totalSickLeaves;

  const CurrentMonthStats({
    this.totalCompleteAttends = 0,
    this.totalIncompleteAttends = 0,
    this.totalAbsents = 0,
    this.totalOfficialHolidays = 0,
    this.totalRequestLeaves = 0,
    this.totalSickLeaves = 0,
  });

  factory CurrentMonthStats.fromJson(Map<String, dynamic> json) {
    return CurrentMonthStats(
      totalCompleteAttends: json['total_complete_attends'] ?? 0,
      totalIncompleteAttends: json['total_incomplete_attends'] ?? 0,
      totalAbsents: json['total_absents'] ?? 0,
      totalOfficialHolidays: json['total_official_holidays'] ?? 0,
      totalRequestLeaves: json['total_request_leaves'] ?? 0,
      totalSickLeaves: json['total_sick_leaves'] ?? 0,
    );
  }

  int get total =>
      totalCompleteAttends +
      totalIncompleteAttends +
      totalAbsents +
      totalOfficialHolidays +
      totalRequestLeaves +
      totalSickLeaves;

  @override
  List<Object?> get props => [
        totalCompleteAttends,
        totalIncompleteAttends,
        totalAbsents,
        totalOfficialHolidays,
        totalRequestLeaves,
        totalSickLeaves,
      ];
}

class Tasks extends Equatable {
  final int active;
  final int finished;

  const Tasks({
    this.active = 0,
    this.finished = 0,
  });

  factory Tasks.fromJson(Map<String, dynamic> json) {
    return Tasks(
      active: json['active'] ?? 0,
      finished: json['finished'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [active, finished];
}

class Contract extends Equatable {
  final String code;
  final String type;
  final String client;
  final String endDate;
  final int remainingDays;

  const Contract({
    this.code = '',
    this.type = '',
    this.client = '',
    this.endDate = '',
    this.remainingDays = 0,
  });

  factory Contract.fromJson(Map<String, dynamic> json) {
    return Contract(
      code: json['code'] ?? '',
      type: json['type'] ?? '',
      client: json['client'] ?? '',
      endDate: json['end_date'] ?? '',
      remainingDays: json['remaining_days'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [code, type, client, endDate, remainingDays];
}

const demoHomeModel = HomeModel(
  employee: Employee(
    empPIC: "http://app.iv4it.com/assets/users/alaabahi.jpg",
    empName: "علاء باهي",
    empJob: "دعم فني",
  ),
  attendance: '10:15',
  currentMonthStats: CurrentMonthStats(
    totalCompleteAttends: 1,
    totalIncompleteAttends: 1,
    totalAbsents: 14,
    totalOfficialHolidays: 0,
    totalRequestLeaves: 0,
    totalSickLeaves: 0,
  ),
  tasks: Tasks(
    active: 0,
    finished: 2,
  ),
  contractsAboutExpire: [
    Contract(
      code: "MB\\RUH\\24\\CC003",
      type: "CC",
      client: "مؤسسة عائض الظافر للتجارة",
      endDate: "2025-06-30",
      remainingDays: 11,
    ),
  ],
);
