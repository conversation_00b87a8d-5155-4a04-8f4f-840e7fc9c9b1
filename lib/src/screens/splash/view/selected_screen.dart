import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_screen.dart';

class SelectedScreen extends ConsumerWidget {
  const SelectedScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(bottomNavigationControllerProvider);
    return Scaffold(
      // bottomNavigationBar: const BottomNavBarWidget(),
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(opacity: animation, child: child);
        },
        child: const HomeScreen(),
        // selectedIndex == 0 ?
        // : const ReportsScreen(),
      ),
    );
  }
}
