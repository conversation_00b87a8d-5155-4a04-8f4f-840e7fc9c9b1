import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:opti_tickets/generated/assets.gen.dart';
import 'package:opti_tickets/src/core/routes/app_routes.dart';
import 'package:opti_tickets/src/core/shared/extensions/animation_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

class SplashScreen extends HookWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final loggedIn = GetStorageService.hasData(key: LocalKeys.token);

    useEffect(() {
      final targetRoute = loggedIn ? AppRoutes.selected : AppRoutes.login;

      Future.delayed(const Duration(seconds: 3), () {
        navService.pushReplacementNamed(targetRoute);
      });

      return () {};
    }, []);

    return Material(
      child: Stack(
        alignment: Alignment.bottomRight,
        children: [
          Center(
            child: Image.asset(
              "assets/animated/splash.gif",
              height: context.height,
              width: context.width,
              // fit: BoxFit.fill,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(right: AppSpaces.screenPadding),
            child: Assets.images.ivLogo
                .image(
                  width: 100,
                  height: 100,
                  fit: BoxFit.cover,
                )
                .rightSlide,
          ),
        ],
      ),
    );
  }
}
