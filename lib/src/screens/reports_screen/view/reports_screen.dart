import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:opti_tickets/src/screens/signatures/providers/signature_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class ReportsScreen extends ConsumerStatefulWidget {
  const ReportsScreen({super.key});

  @override
  ConsumerState<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends ConsumerState<ReportsScreen> {
  DateTime startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime endDate = DateTime.now();
  UserModel? selectedUser;
  List<SignatureModel> reportData = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUsers();
      _generateReport();
    });
  }

  Future<void> _loadUsers() async {
    await ref.read(signatureControllerProvider).getAllUsers();
  }

  Future<void> _generateReport() async {
    setState(() {
      isLoading = true;
    });

    try {
      final controller = ref.read(signatureControllerProvider);
      final data = await controller.getSignaturesByDateRange(
        startDate: startDate,
        endDate: endDate,
        uid: selectedUser?.uid,
      );

      setState(() {
        reportData = data;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      XrHelper.showSnackBar(
        context,
        message: e.toString(),
        type: SnackBarType.error,
      );
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: startDate, end: endDate),
    );

    if (picked != null) {
      setState(() {
        startDate = picked.start;
        endDate = picked.end;
      });
      _generateReport();
    }
  }

  void _onUserSelected(UserModel? user) {
    setState(() {
      selectedUser = user;
    });
    _generateReport();
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = UserModel.currentUser();

    // Check if user is admin
    if (!currentUser.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: Text(context.tr.reports),
        ),
        body: const Center(
          child: Text('Access Denied: Admin only'),
        ),
      );
    }

    final signatureController = ref.watch(signatureControllerProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr.reports),
        backgroundColor: ColorManager.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: reportData.isNotEmpty ? _exportReport : null,
          ),
        ],
      ),
      body: Column(
        children: [
          // Filters section
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Date range selector
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            context.tr.dateRange,
                            style: context.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          InkWell(
                            onTap: _selectDateRange,
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.date_range),
                                  const SizedBox(width: 8),
                                  Text(
                                    '${startDate.formatDateToString} - ${endDate.formatDateToString}',
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // User selector
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.selectDelivery,
                      style: context.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<UserModel>(
                      value: selectedUser,
                      decoration: InputDecoration(
                        hintText: context.tr.allDeliveries,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: [
                        DropdownMenuItem<UserModel>(
                          value: null,
                          child: Text(context.tr.allDeliveries),
                        ),
                        ...signatureController.users.map(
                          (user) => DropdownMenuItem<UserModel>(
                            value: user,
                            child: Text(user.name),
                          ),
                        ),
                      ],
                      onChanged: _onUserSelected,
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Report data
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : reportData.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.assessment_outlined,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No data found for selected criteria',
                              style: context.textTheme.titleMedium?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: reportData.length,
                        itemBuilder: (context, index) {
                          final signature = reportData[index];
                          return _buildReportCard(signature);
                        },
                      ),
          ),

          // Summary section
          if (reportData.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildSummaryItem(
                    'Total Signatures',
                    reportData.length.toString(),
                    Icons.assignment,
                  ),
                  _buildSummaryItem(
                    'First of Day',
                    reportData.where((s) => s.isFirstOfDay).length.toString(),
                    Icons.wb_sunny,
                  ),
                  _buildSummaryItem(
                    'Regular',
                    reportData.where((s) => !s.isFirstOfDay).length.toString(),
                    Icons.access_time,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildReportCard(SignatureModel signature) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: signature.isFirstOfDay
            ? Border.all(color: Colors.green, width: 2)
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: signature.isFirstOfDay
                      ? Colors.green.shade100
                      : Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  signature.isFirstOfDay
                      ? context.tr.firstOfDay
                      : context.tr.regularSignature,
                  style: TextStyle(
                    color: signature.isFirstOfDay
                        ? Colors.green.shade700
                        : Colors.blue.shade700,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                signature.formattedDate,
                style: context.textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.person, color: ColorManager.primaryColor, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  signature.mandobName,
                  style: context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                signature.formattedTime,
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.grey[600], size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  signature.place,
                  style: context.textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: ColorManager.primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: context.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: ColorManager.primaryColor,
          ),
        ),
        Text(
          label,
          style: context.textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  void _exportReport() {
    // TODO: Implement PDF/Excel export functionality
    XrHelper.showSnackBar(
      context,
      message: 'Export functionality will be implemented',
      type: SnackBarType.info,
    );
  }
}
