import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

class ReportFieldsWidget extends StatelessWidget {
  final ValueNotifier<DateTime?> selectedStartDateNotifier;
  final ValueNotifier<DateTime?> selectedEndDateNotifier;

  const ReportFieldsWidget(
      {super.key,
      required this.selectedStartDateNotifier,
      required this.selectedEndDateNotifier});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: BaseDatePicker(
                    selectedDateNotifier: selectedStartDateNotifier,
                    label: context.tr.startDate),
              ),
              AppGaps.gap12,
              Expanded(
                child: BaseDatePicker(
                    selectedDateNotifier: selectedEndDateNotifier,
                    label: context.tr.endDate),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
