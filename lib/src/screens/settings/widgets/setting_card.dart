import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class SettingCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final Widget child;

  const SettingCard(
      {super.key,
      required this.child,
      required this.icon,
      required this.title});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpaces.padding8),
      child: Column(
        children: [
          Row(mainAxisAlignment: MainAxisAlignment.start, children: [
            Container(
              padding: const EdgeInsets.all(AppSpaces.padding4),
              decoration: BoxDecoration(
                color: ColorManager.primaryColor.withOpacity(.2),
                borderRadius: BorderRadius.circular(AppSpaces.padding12),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).primaryColor,
                size: AppSpaces.padding24,
              ),
            ),
            AppGaps.gap12,
            Expanded(
                child: Text(
              title,
              maxLines: 1,
              style: AppTextStyles.subTitle,
            )),
            child
          ]),
        ],
      ),
    );
  }
}
