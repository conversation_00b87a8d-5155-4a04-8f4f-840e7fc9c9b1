// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(name) => "Welcome, ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "absents": MessageLookupByLibrary.simpleMessage("Absents"),
    "active": MessageLookupByLibrary.simpleMessage("Active"),
    "activeTasks": MessageLookupByLibrary.simpleMessage("Active Tasks"),
    "addNewTicket": MessageLookupByLibrary.simpleMessage("Add New Ticket"),
    "addSignature": MessageLookupByLibrary.simpleMessage("Add Signature"),
    "admin": MessageLookupByLibrary.simpleMessage("Admin"),
    "allDeliveries": MessageLookupByLibrary.simpleMessage("All Deliveries"),
    "allTickets": MessageLookupByLibrary.simpleMessage("All Tickets"),
    "alreadyHaveAccount": MessageLookupByLibrary.simpleMessage(
      "Already have an account?",
    ),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "archived": MessageLookupByLibrary.simpleMessage("Archived"),
    "areYouSureYouWantToLogout": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to logout?",
    ),
    "ascending": MessageLookupByLibrary.simpleMessage("Ascending"),
    "attachment": MessageLookupByLibrary.simpleMessage("Attachment"),
    "attendanceTime": MessageLookupByLibrary.simpleMessage("Attendance Time"),
    "authenticate": MessageLookupByLibrary.simpleMessage("Authenticate"),
    "authenticationRequired": MessageLookupByLibrary.simpleMessage(
      "Authentication Required",
    ),
    "biometricNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Biometric authentication not available",
    ),
    "camera": MessageLookupByLibrary.simpleMessage("Camera"),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("Change Language"),
    "checkIn": MessageLookupByLibrary.simpleMessage("Check In"),
    "completeAttends": MessageLookupByLibrary.simpleMessage("Complete Attends"),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "coordinates": MessageLookupByLibrary.simpleMessage("Coordinates"),
    "createAccount": MessageLookupByLibrary.simpleMessage("Create Account"),
    "currentTime": MessageLookupByLibrary.simpleMessage("Current Time"),
    "dark": MessageLookupByLibrary.simpleMessage("Dark"),
    "dateRange": MessageLookupByLibrary.simpleMessage("Date Range"),
    "days": MessageLookupByLibrary.simpleMessage("Days"),
    "descending": MessageLookupByLibrary.simpleMessage("Descending"),
    "description": MessageLookupByLibrary.simpleMessage("Description"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage(
      "Don\'t have an account?",
    ),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "emailAlreadyInUse": MessageLookupByLibrary.simpleMessage(
      "Email already in use",
    ),
    "endDate": MessageLookupByLibrary.simpleMessage("End Date"),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "enter": MessageLookupByLibrary.simpleMessage("Enter"),
    "enterPlace": MessageLookupByLibrary.simpleMessage("Enter Place"),
    "export": MessageLookupByLibrary.simpleMessage("Export"),
    "exportExcel": MessageLookupByLibrary.simpleMessage("Export Excel"),
    "exportPdf": MessageLookupByLibrary.simpleMessage("Export PDF"),
    "fingerprint": MessageLookupByLibrary.simpleMessage("Fingerprint"),
    "finishedTasks": MessageLookupByLibrary.simpleMessage("Finished Tasks"),
    "firstOfDay": MessageLookupByLibrary.simpleMessage("First of Day"),
    "firstSignatureOfDay": MessageLookupByLibrary.simpleMessage(
      "First Signature of Day",
    ),
    "fromDate": MessageLookupByLibrary.simpleMessage("From Date"),
    "gallery": MessageLookupByLibrary.simpleMessage("Gallery"),
    "home": MessageLookupByLibrary.simpleMessage("Home"),
    "incompleteAttends": MessageLookupByLibrary.simpleMessage(
      "Incomplete Attends",
    ),
    "invalidCredentials": MessageLookupByLibrary.simpleMessage(
      "Invalid credentials",
    ),
    "issue": MessageLookupByLibrary.simpleMessage("Issue"),
    "issuerEmail": MessageLookupByLibrary.simpleMessage("Issuer Email"),
    "issuerName": MessageLookupByLibrary.simpleMessage("Issuer Name"),
    "issuerPhone": MessageLookupByLibrary.simpleMessage("Issuer Phone"),
    "itsGreatToSeeYou": MessageLookupByLibrary.simpleMessage(
      "It\'s great to see you",
    ),
    "language": MessageLookupByLibrary.simpleMessage("Language"),
    "light": MessageLookupByLibrary.simpleMessage("Light"),
    "location": MessageLookupByLibrary.simpleMessage("Location"),
    "locationPermissionDenied": MessageLookupByLibrary.simpleMessage(
      "Location permission denied",
    ),
    "locationRequired": MessageLookupByLibrary.simpleMessage(
      "Location Required",
    ),
    "login": MessageLookupByLibrary.simpleMessage("Login"),
    "loginFailed": MessageLookupByLibrary.simpleMessage("Login failed"),
    "logout": MessageLookupByLibrary.simpleMessage("Logout"),
    "mandob": MessageLookupByLibrary.simpleMessage("Delivery"),
    "monthlyStats": MessageLookupByLibrary.simpleMessage("Monthly Statistics"),
    "mySubscriptions": MessageLookupByLibrary.simpleMessage("My Subscriptions"),
    "name": MessageLookupByLibrary.simpleMessage("Name"),
    "networkError": MessageLookupByLibrary.simpleMessage("Network error"),
    "newReplyOnTicket": MessageLookupByLibrary.simpleMessage(
      "New reply on ticket",
    ),
    "noDataFound": MessageLookupByLibrary.simpleMessage("No data found"),
    "noRepliesFound": MessageLookupByLibrary.simpleMessage("No replies found"),
    "noSignaturesToday": MessageLookupByLibrary.simpleMessage(
      "No signatures today",
    ),
    "office": MessageLookupByLibrary.simpleMessage("Office"),
    "officialHolidays": MessageLookupByLibrary.simpleMessage(
      "Official Holidays",
    ),
    "password": MessageLookupByLibrary.simpleMessage("Password"),
    "pickImage": MessageLookupByLibrary.simpleMessage("Pick Image"),
    "place": MessageLookupByLibrary.simpleMessage("Place"),
    "pleaseAuthenticateToAddSignature": MessageLookupByLibrary.simpleMessage(
      "Please authenticate to add signature",
    ),
    "recentActiveTickets": MessageLookupByLibrary.simpleMessage(
      "Recent Active Tickets",
    ),
    "register": MessageLookupByLibrary.simpleMessage("Register"),
    "registrationFailed": MessageLookupByLibrary.simpleMessage(
      "Registration failed",
    ),
    "registrationSuccessful": MessageLookupByLibrary.simpleMessage(
      "Registration successful",
    ),
    "regularSignature": MessageLookupByLibrary.simpleMessage(
      "Regular Signature",
    ),
    "remainingMaintenance": MessageLookupByLibrary.simpleMessage(
      "Remaining Maintenance",
    ),
    "repliedOnTheTicket": MessageLookupByLibrary.simpleMessage(
      "Replied on the ticket",
    ),
    "replies": MessageLookupByLibrary.simpleMessage("Replies"),
    "reply": MessageLookupByLibrary.simpleMessage("Reply"),
    "replyCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "Reply cannot be empty",
    ),
    "replySentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Reply sent successfully",
    ),
    "reports": MessageLookupByLibrary.simpleMessage("Reports"),
    "request": MessageLookupByLibrary.simpleMessage("Request"),
    "requestLeaves": MessageLookupByLibrary.simpleMessage("Request Leaves"),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "selectDate": MessageLookupByLibrary.simpleMessage("Select Date"),
    "selectDelivery": MessageLookupByLibrary.simpleMessage("Select Delivery"),
    "settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "sickLeaves": MessageLookupByLibrary.simpleMessage("Sick Leaves"),
    "signUp": MessageLookupByLibrary.simpleMessage("Sign Up"),
    "signature": MessageLookupByLibrary.simpleMessage("Signature"),
    "signatureAddFailed": MessageLookupByLibrary.simpleMessage(
      "Failed to add signature",
    ),
    "signatureAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Signature added successfully",
    ),
    "signatureLocation": MessageLookupByLibrary.simpleMessage(
      "Signature Location",
    ),
    "signaturePlace": MessageLookupByLibrary.simpleMessage("Signature Place"),
    "signatureTime": MessageLookupByLibrary.simpleMessage("Signature Time"),
    "signatures": MessageLookupByLibrary.simpleMessage("Signatures"),
    "somethingWentWrong": MessageLookupByLibrary.simpleMessage(
      "Something went wrong",
    ),
    "sortByDate": MessageLookupByLibrary.simpleMessage("Sort by Date"),
    "startDate": MessageLookupByLibrary.simpleMessage("Start Date"),
    "status": MessageLookupByLibrary.simpleMessage("Status"),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "summary": MessageLookupByLibrary.simpleMessage("Summary"),
    "systemSetting": MessageLookupByLibrary.simpleMessage("System Setting"),
    "theme": MessageLookupByLibrary.simpleMessage("Theme"),
    "toDate": MessageLookupByLibrary.simpleMessage("To Date"),
    "todaySignatures": MessageLookupByLibrary.simpleMessage(
      "Today\'s Signatures",
    ),
    "totalTickets": MessageLookupByLibrary.simpleMessage("Total Tickets"),
    "userNotFound": MessageLookupByLibrary.simpleMessage("User not found"),
    "username": MessageLookupByLibrary.simpleMessage("Username"),
    "weakPassword": MessageLookupByLibrary.simpleMessage(
      "Password is too weak",
    ),
    "welcomeBack": MessageLookupByLibrary.simpleMessage("Welcome back"),
    "welcomeBackLine": MessageLookupByLibrary.simpleMessage("Welcome\nback"),
    "welcomeWithName": m0,
  };
}
