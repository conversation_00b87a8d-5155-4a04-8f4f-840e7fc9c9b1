part of xr_helper;

extension NavigationServiceExtensions on Widget {
  void get navigate {
    navService.push(MaterialPageRoute(
      builder: (context) => this,
    ));
  }

  void get navigateReplacement {
    navService.pushReplacement(
      MaterialPageRoute(
        builder: (context) => this,
      ),
    );
  }

  void navigateReplacementWithResult({Object? result}) {
    navService.pushReplacement(
      MaterialPageRoute(
        builder: (context) => this,
      ),
      result: result,
    );
  }

  void back<T extends Object?>({T? result}) {
    navService.back<T>(result: result);
  }
}
