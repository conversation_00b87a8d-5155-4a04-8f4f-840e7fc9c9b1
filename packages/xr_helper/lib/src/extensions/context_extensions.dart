part of xr_helper;

extension SizeExensions on BuildContext {
  //? Get size shortcuts
  double get height => MediaQuery.sizeOf(this).height;

  double get width => MediaQuery.sizeOf(this).width;
}

extension Navigation on BuildContext {
  void back([dynamic result]) {
    Navigator.of(this).pop(result);
  }
}

extension Appearance on BuildContext {
  bool get isDark {
    final bool isDarkMode = Theme.of(this).brightness == Brightness.dark;

    return isDarkMode;
  }
}
