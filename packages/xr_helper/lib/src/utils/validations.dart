part of xr_helper;

class Validations {
  //! Password Validation
  static final password = FormBuilderValidators.compose([
    FormBuilderValidators.required(),
    FormBuilderValidators.minLength(3),
  ]);

  // ! Phone Number Validation

  static final phoneNumber = FormBuilderValidators.compose([
    FormBuilderValidators.required(),
    FormBuilderValidators.numeric(),
    FormBuilderValidators.minLength(10),
    FormBuilderValidators.maxLength(15),
  ]);

  //! Numbers Only Validation
  static final numbersOnly = FormBuilderValidators.compose([
    FormBuilderValidators.required(),
    FormBuilderValidators.numeric(),
  ]);

  //! Email Validation
  static final email = FormBuilderValidators.compose([
    FormBuilderValidators.required(),
    FormBuilderValidators.email(),
  ]);

  //! Must Be Not Empty Validation
  static final mustBeNotEmpty = FormBuilderValidators.compose([
    FormBuilderValidators.required(),
  ]);
}
