part of xr_helper;

FToast toastAlert = FToast();

void showToast(String msg,
    {Color iconColor = Colors.white,
    bool isError = false,
    IconData icon = Icons.check_circle_outline,
    String? title,
    Color? color,
    ToastGravity gravity = ToastGravity.TOP,
    Duration duration = const Duration(seconds: 2)}) {
  Widget toast = Container(
    padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(10.0),
      color: color ?? (isError ? Colors.red : const Color(0xFF4CAF50)),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(isError ? Icons.error : icon, size: 28.0, color: iconColor),
        const SizedBox(width: 12.0),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (title != null)
                Text(title,
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.white)),
              Text(msg, style: const TextStyle(color: Colors.white)),
            ],
          ),
        ),
      ],
    ),
  );

  toastAlert.showToast(
    child: toast,
    gravity: gravity,
    toastDuration: duration,
  );
}
