part of xr_helper;

class BaseDropDown extends StatelessWidget {
  final dynamic selectedValue;
  final String? label;
  final List<dynamic> data;
  final void Function(dynamic)? onChanged;
  final Widget? icon;
  final String Function(dynamic)? asString;
  final bool isRequired;
  final bool showTitle;
  final bool isWhiteText;

  const BaseDropDown({
    super.key,
    required this.onChanged,
    this.asString,
    required this.data,
    required this.label,
    required this.selectedValue,
    this.isRequired = true,
    this.showTitle = true,
    this.isWhiteText = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColors.white,
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton(
          borderRadius: BorderRadius.circular(12),
          value: selectedValue,
          isExpanded: true,
          items: data.map((e) {
            return DropdownMenuItem(
              value: e,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  asString != null ? asString!(e) : e.toString(),
                  style: isWhiteText
                      ? AppTextStyles.whiteLabelMedium
                      : AppTextStyles.labelLarge,
                ),
              ),
            );
          }).toList(),
          onChanged: onChanged,
          icon: const Icon(Icons.keyboard_arrow_down_outlined),
          hint: Text(
            label!,
            style: isWhiteText
                ? AppTextStyles.whiteLabelMedium
                : AppTextStyles.labelMedium,
          ),
        ),
      ),
    );
  }
}
