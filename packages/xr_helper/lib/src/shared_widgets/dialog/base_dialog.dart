part of xr_helper;

class BaseDialog extends StatefulWidget {
  final Widget child;
  final double radius;
  final Color color;

  const BaseDialog({
    super.key,
    required this.child,
    this.radius = AppRadius.radius8,
    this.color = Colors.white,
  });

  @override
  State<StatefulWidget> createState() => _MainDialog();
}

class _MainDialog extends State<BaseDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: ScaleTransition(
          scale: scaleAnimation,
          child: Container(
              margin: const EdgeInsets.all(15),
              decoration: ShapeDecoration(
                  color: widget.color,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(widget.radius))),
              child: widget.child),
        ),
      ),
    );
  }
}
