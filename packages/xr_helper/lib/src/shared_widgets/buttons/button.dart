part of xr_helper;

class <PERSON><PERSON> extends HookWidget {
  final String label;
  final Widget? icon;
  final Widget? loadingWidget;
  final bool haveElevation;
  final void Function()? onPressed;
  final Color? color;
  final bool isPrefixIcon;
  final bool isOutLine;
  final bool isWhiteText;
  final bool isBold;
  final bool enabled;
  final bool isLoading;
  final Color? textColor;
  final double? radius;

  const Button({
    super.key,
    required this.label,
    this.haveElevation = true,
    required this.onPressed,
    this.icon,
    this.isPrefixIcon = false,
    this.isOutLine = false,
    this.isWhiteText = true,
    this.color,
    this.isBold = true,
    this.enabled = true,
    this.isLoading = false,
    this.textColor,
    this.loadingWidget,
    this.radius,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyle = useState<ButtonStyle?>(null);

    return Consumer<AppConfig>(
      builder: (context, appConfig, child) {
        void init() {
          buttonStyle.value = appConfig.buttonStyle?.copyWith(
                surfaceTintColor: MaterialStateProperty.all(color),
                backgroundColor: MaterialStateProperty.all(
                    isOutLine || !enabled ? Colors.transparent : color),
              ) ??
              ElevatedButton.styleFrom(
                surfaceTintColor: color,
                elevation: haveElevation && !isOutLine && enabled ? 3 : 1,
                foregroundColor:
                    isOutLine ? color!.withOpacity(.1) : Colors.white,
                backgroundColor:
                    isOutLine || !enabled ? Colors.transparent : color,
                shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(radius ?? AppRadius.radius8),
                ),
                side: isOutLine
                    ? BorderSide(
                        color: color!,
                        width: 1,
                      )
                    : BorderSide.none,
              );
        }

        return HookBuilder(builder: (context) {
          useEffect(() {
            WidgetsBinding.instance.addPostFrameCallback((_) => init());

            return () {};
          }, []);

          return AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            width: double.infinity,
            child: isLoading
                ? loadingWidget ?? const LinearProgressIndicator()
                : SizedBox(
                    height: 55,
                    child: ElevatedButton(
                        onPressed: onPressed,
                        style: buttonStyle.value,
                        child: _buildChild(context)),
                  ),
          );
        });
      },
    );
  }

  Widget _buildChild(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isPrefixIcon && icon != null)
          Row(
            children: [
              icon!,
              AppGaps.gap12,
            ],
          ),
        Expanded(
          child: Center(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                label,
                maxLines: 1,
                style: isWhiteText
                    ? AppTextStyles.whiteSubTitle.copyWith(
                        color: isOutLine ? color : null,
                        fontWeight: isBold ? FontWeight.bold : null,
                      )
                    : AppTextStyles.subTitle.copyWith(
                        fontWeight: isBold ? FontWeight.bold : null,
                        color: textColor,
                      ),
              ),
            ),
          ),
        ),
        if (icon != null && !isPrefixIcon)
          Row(
            children: [
              AppGaps.gap12,
              icon!,
            ],
          )
      ],
    );
  }
}
