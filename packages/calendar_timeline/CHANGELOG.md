## 1.1.2 - 14/03/2023

* Minor updates

## 1.1.1 - 09/02/2023

* Fix overflow error in day widget

## 1.1.0 - 12/12/2022

* Integrate a CI system
* Add 'shrink' param to save more space
* Thanks @pab<PERSON>jimpas and @HafzalBZ

## 1.0.5 - 05/07/2022

* Improve behavior and fixed errors
* Add web support and migrate to null safety example project
* Thanks @ZachGonzalezz and @manuelperez96

## 1.0.4 - 22/11/2021

* Fix error when not set locale parameter
* Final null-safety version

## 1.0.3-null-safety.0 - 29/09/2021

* Fix error locale data has not been initialized

## 1.0.2-null-safety.0 - 28/09/2021

* Add widget testing
* Fix error with padding in different screens widths

## 1.0.1-null-safety.0 - 11/05/2021

* Fix error with intl if flutter_localization not configured

## 1.0.0-null-safety.0 - 19/03/2021

* Migrate to null safety

## 0.7.1 - 09/03/2021

* Update packages

## 0.7.0 - 17/12/2020

* Added option to see years individually. Thanks to https://github.com/Flucadetena.

## 0.6.3 - 10/08/2020

* Fix error when generating the abbreviation of the day of the week in different locales
* Show the selected day when regenerating the days after pressing a month
* Upgrade dependencies

## 0.6.2 - 26/06/2020

* Check local localizations for intl default config
* Allow the last item in the listings to be left aligned

## 0.6.1 - 17/06/2020

* Update widget if state changes

## 0.6.0 - 03/06/2020

* Added locale parameter

## 0.5.0 - 05/05/2020

* Add dayNameColor parameter

## 0.4.0 - 29/04/2020

* Modify design year item

## 0.3.1 - 23/04/2020

* Fix error if initialDate and firstDate are the same day

## 0.3.0 - 22/04/2020

* Add dotColor parameter
* Show year with 4 digits and not show it like first element in month list
* Fix design errors

## 0.2.0 - 22/04/2020

* Show year in first element of month list
* Add parameters description to readme
* Modify example design

## 0.1.0 - 21/04/2020

* Add color customization
* Add selectableDayPredicate functionality
* Complete example app
* Fix errors
* Refactor

## 0.0.1 - 16/04/2020

* Initial version.
